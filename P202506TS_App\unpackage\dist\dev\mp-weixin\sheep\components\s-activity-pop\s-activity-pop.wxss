/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.model-box.data-v-c5b2a531 {
  height: 60vh;
}
.model-box .title.data-v-c5b2a531 {
  justify-content: center;
  font-size: 36rpx;
  height: 80rpx;
  font-weight: bold;
  color: #333333;
}
.model-content.data-v-c5b2a531 {
  height: -webkit-fit-content;
  height: fit-content;
  max-height: 380rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  margin-top: 20rpx;
}
.model-content .model-content-tag.data-v-c5b2a531 {
  font-size: 35rpx;
  font-weight: 500;
  color: #ff6911;
  line-height: 150rpx;
  width: 200rpx;
  height: 150rpx;
  text-align: center;
}
.model-content .model-content-title.data-v-c5b2a531 {
  width: 450rpx;
  height: 150rpx;
  font-size: 26rpx;
  font-weight: 500;
  color: #333333;
  overflow: hidden;
}
.model-content .cicon-forward.data-v-c5b2a531 {
  font-size: 28rpx;
  color: #999999;
  margin: 0 auto;
}
.titleLi.data-v-c5b2a531 {
  margin: 10rpx 0 10rpx 20rpx;
  font-size: 26rpx;
}
.actBox.data-v-c5b2a531 {
  width: 700rpx;
  height: 150rpx;
  background-color: #fff2f2;
  margin: 10rpx auto;
  border-radius: 10rpx;
}
.boxCont.data-v-c5b2a531 {
  width: 700rpx;
  height: 150rpx;
  align-items: center;
}
.contBu.data-v-c5b2a531 {
  height: 80rpx;
  line-height: 80rpx;
  overflow: hidden;
  font-size: 30rpx;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}
.cotBu-txt.data-v-c5b2a531 {
  height: 70rpx;
  line-height: 70rpx;
  font-size: 25rpx;
  color: #999999;
}
.model-content-tag2.data-v-c5b2a531 {
  font-size: 35rpx;
  font-weight: 500;
  color: #ff6911;
  width: 200rpx;
  height: 150rpx;
  text-align: center;
}
.usePrice.data-v-c5b2a531 {
  width: 200rpx;
  height: 90rpx;
  line-height: 100rpx;
}
.impose.data-v-c5b2a531 {
  width: 200rpx;
  height: 50rpx;
  font-size: 23rpx;
}
.model-content-title2.data-v-c5b2a531 {
  width: 330rpx;
  height: 150rpx;
  font-size: 26rpx;
  font-weight: 500;
  color: #333333;
  overflow: hidden;
}
.coupon.data-v-c5b2a531 {
  width: 150rpx;
  height: 50rpx;
  line-height: 50rpx;
  background-color: #ff4444;
  color: white;
  border-radius: 30rpx;
  text-align: center;
  font-size: 25rpx;
}
.coupon2.data-v-c5b2a531 {
  width: 150rpx;
  height: 50rpx;
  line-height: 50rpx;
  background-color: #cbc0bf;
  color: white;
  border-radius: 30rpx;
  text-align: center;
  font-size: 25rpx;
}
.nullBox.data-v-c5b2a531 {
  width: 100%;
  height: 300rpx;
  font-size: 25rpx;
  line-height: 300rpx;
  text-align: center;
  color: #999999;
}