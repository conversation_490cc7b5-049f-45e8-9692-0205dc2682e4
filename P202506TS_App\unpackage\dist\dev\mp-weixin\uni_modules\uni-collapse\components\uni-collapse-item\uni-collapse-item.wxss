/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-collapse-item {
  box-sizing: border-box;
}
.uni-collapse-item__title {
  display: flex;
  width: 100%;
  box-sizing: border-box;
  flex-direction: row;
  align-items: center;
  transition: border-bottom-color 0.3s;
}
.uni-collapse-item__title-wrap {
  width: 100%;
  flex: 1;
}
.uni-collapse-item__title-box {
  padding: 0 15px;
  display: flex;
  width: 100%;
  box-sizing: border-box;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 48px;
  line-height: 48px;
  background-color: #fff;
  color: #303133;
  font-size: 13px;
  font-weight: 500;
}
.uni-collapse-item__title-box.is-disabled .uni-collapse-item__title-text {
  color: #999;
}
.uni-collapse-item__title.uni-collapse-item-border {
  border-bottom: 1px solid #ebeef5;
}
.uni-collapse-item__title.is-open {
  border-bottom-color: transparent;
}
.uni-collapse-item__title-img {
  height: 22px;
  width: 22px;
  margin-right: 10px;
}
.uni-collapse-item__title-text {
  flex: 1;
  font-size: 14px;
  white-space: nowrap;
  color: inherit;
  overflow: hidden;
  text-overflow: ellipsis;
}
.uni-collapse-item__title-arrow {
  display: flex;
  box-sizing: border-box;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 10px;
  transform: rotate(0deg);
}
.uni-collapse-item__title-arrow-active {
  transform: rotate(-180deg);
}
.uni-collapse-item__wrap {
  will-change: height;
  box-sizing: border-box;
  background-color: #fff;
  overflow: hidden;
  position: relative;
  height: 0;
}
.uni-collapse-item__wrap.is--transition {
  transition-property: height, border-bottom-width;
  transition-duration: 0.3s;
  will-change: height;
}
.uni-collapse-item__wrap-content {
  position: absolute;
  font-size: 13px;
  color: #303133;
  border-bottom-color: transparent;
  border-bottom-style: solid;
  border-bottom-width: 0;
}
.uni-collapse-item__wrap-content.uni-collapse-item--border {
  border-bottom-width: 1px;
  border-bottom-color: red;
  border-bottom-color: #ebeef5;
}
.uni-collapse-item__wrap-content.open {
  position: relative;
}
.uni-collapse-item--animation {
  transition-property: transform;
  transition-duration: 0.3s;
  transition-timing-function: ease;
}