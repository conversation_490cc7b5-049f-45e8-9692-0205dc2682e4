<view class="{{['uni-easyinput', ac && 'uni-easyinput-error']}}" style="{{'color:' + ad}}"><view class="{{['uni-easyinput__content', W && 'is-input-border', X && 'is-input-error-border', Y && 'is-textarea', Z && 'is-disabled']}}" style="{{'border-color:' + aa + ';' + ('background-color:' + ab)}}"><uni-icons wx:if="{{a}}" class="content-clear-icon" bindclick="{{b}}" u-i="66de5853-0" bind:__l="__l" u-p="{{c}}"></uni-icons><textarea wx:if="{{d}}" class="{{['uni-easyinput__content-textarea', e && 'input-padding']}}" name="{{f}}" value="{{g}}" placeholder="{{h}}" placeholderStyle="{{i}}" disabled="{{j}}" placeholder-class="uni-easyinput__placeholder-class" maxlength="{{k}}" focus="{{l}}" autoHeight="{{m}}" bindinput="{{n}}" bindblur="{{o}}" bindfocus="{{p}}" bindconfirm="{{q}}"></textarea><block wx:else><input wx:if="{{r0}}" type="{{r}}" class="uni-easyinput__content-input" style="{{'padding-right:' + s + ';' + ('padding-left:' + t)}}" name="{{v}}" value="{{w}}" password="{{x}}" placeholder="{{y}}" placeholderStyle="{{z}}" placeholder-class="uni-easyinput__placeholder-class" disabled="{{A}}" maxlength="{{B}}" focus="{{C}}" confirmType="{{D}}" bindfocus="{{E}}" bindblur="{{F}}" bindinput="{{G}}" bindchange="{{H}}" bindconfirm="{{I}}" cursor-spacing="{{30}}" always-embed/></block><block wx:if="{{J}}"><uni-icons wx:if="{{K}}" class="{{['content-clear-icon', L && 'is-textarea-icon']}}" bindclick="{{M}}" u-i="66de5853-1" bind:__l="__l" u-p="{{N}}"></uni-icons></block><block wx:elif="{{O}}"><uni-icons wx:if="{{P}}" class="content-clear-icon" bindclick="{{Q}}" u-i="66de5853-2" bind:__l="__l" u-p="{{R}}"></uni-icons></block><block wx:else><uni-icons wx:if="{{S}}" class="{{['content-clear-icon', T && 'is-textarea-icon']}}" bindclick="{{U}}" u-i="66de5853-3" bind:__l="__l" u-p="{{V}}"></uni-icons></block><slot name="right"></slot></view></view>