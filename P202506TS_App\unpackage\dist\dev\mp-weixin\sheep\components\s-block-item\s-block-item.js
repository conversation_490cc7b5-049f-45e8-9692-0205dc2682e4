"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _easycom_s_search_block2 = common_vendor.resolveComponent("s-search-block");
  const _easycom_s_notice_block2 = common_vendor.resolveComponent("s-notice-block");
  const _easycom_s_menu_button2 = common_vendor.resolveComponent("s-menu-button");
  const _easycom_s_menu_list2 = common_vendor.resolveComponent("s-menu-list");
  const _easycom_s_menu_grid2 = common_vendor.resolveComponent("s-menu-grid");
  const _easycom_s_popup_image2 = common_vendor.resolveComponent("s-popup-image");
  const _easycom_s_float_menu2 = common_vendor.resolveComponent("s-float-menu");
  const _easycom_s_image_block2 = common_vendor.resolveComponent("s-image-block");
  const _easycom_s_image_banner2 = common_vendor.resolveComponent("s-image-banner");
  const _easycom_s_title_block2 = common_vendor.resolveComponent("s-title-block");
  const _easycom_s_image_cube2 = common_vendor.resolveComponent("s-image-cube");
  const _easycom_s_video_block2 = common_vendor.resolveComponent("s-video-block");
  const _easycom_s_line_block2 = common_vendor.resolveComponent("s-line-block");
  const _easycom_s_hotzone_block2 = common_vendor.resolveComponent("s-hotzone-block");
  const _easycom_s_goods_card2 = common_vendor.resolveComponent("s-goods-card");
  const _easycom_s_goods_shelves2 = common_vendor.resolveComponent("s-goods-shelves");
  const _easycom_s_groupon_block2 = common_vendor.resolveComponent("s-groupon-block");
  const _easycom_s_seckill_block2 = common_vendor.resolveComponent("s-seckill-block");
  const _easycom_s_point_block2 = common_vendor.resolveComponent("s-point-block");
  const _easycom_s_live_block2 = common_vendor.resolveComponent("s-live-block");
  const _easycom_s_coupon_block2 = common_vendor.resolveComponent("s-coupon-block");
  const _easycom_s_richtext_block2 = common_vendor.resolveComponent("s-richtext-block");
  const _easycom_s_user_card2 = common_vendor.resolveComponent("s-user-card");
  const _easycom_s_order_card2 = common_vendor.resolveComponent("s-order-card");
  const _easycom_s_wallet_card2 = common_vendor.resolveComponent("s-wallet-card");
  const _easycom_s_coupon_card2 = common_vendor.resolveComponent("s-coupon-card");
  (_easycom_s_search_block2 + _easycom_s_notice_block2 + _easycom_s_menu_button2 + _easycom_s_menu_list2 + _easycom_s_menu_grid2 + _easycom_s_popup_image2 + _easycom_s_float_menu2 + _easycom_s_image_block2 + _easycom_s_image_banner2 + _easycom_s_title_block2 + _easycom_s_image_cube2 + _easycom_s_video_block2 + _easycom_s_line_block2 + _easycom_s_hotzone_block2 + _easycom_s_goods_card2 + _easycom_s_goods_shelves2 + _easycom_s_groupon_block2 + _easycom_s_seckill_block2 + _easycom_s_point_block2 + _easycom_s_live_block2 + _easycom_s_coupon_block2 + _easycom_s_richtext_block2 + _easycom_s_user_card2 + _easycom_s_order_card2 + _easycom_s_wallet_card2 + _easycom_s_coupon_card2)();
}
const _easycom_s_search_block = () => "../s-search-block/s-search-block.js";
const _easycom_s_notice_block = () => "../s-notice-block/s-notice-block.js";
const _easycom_s_menu_button = () => "../s-menu-button/s-menu-button.js";
const _easycom_s_menu_list = () => "../s-menu-list/s-menu-list.js";
const _easycom_s_menu_grid = () => "../s-menu-grid/s-menu-grid.js";
const _easycom_s_popup_image = () => "../s-popup-image/s-popup-image.js";
const _easycom_s_float_menu = () => "../s-float-menu/s-float-menu.js";
const _easycom_s_image_block = () => "../s-image-block/s-image-block.js";
const _easycom_s_image_banner = () => "../s-image-banner/s-image-banner.js";
const _easycom_s_title_block = () => "../s-title-block/s-title-block.js";
const _easycom_s_image_cube = () => "../s-image-cube/s-image-cube.js";
const _easycom_s_video_block = () => "../s-video-block/s-video-block.js";
const _easycom_s_line_block = () => "../s-line-block/s-line-block.js";
const _easycom_s_hotzone_block = () => "../s-hotzone-block/s-hotzone-block.js";
const _easycom_s_goods_card = () => "../s-goods-card/s-goods-card.js";
const _easycom_s_goods_shelves = () => "../s-goods-shelves/s-goods-shelves.js";
const _easycom_s_groupon_block = () => "../s-groupon-block/s-groupon-block.js";
const _easycom_s_seckill_block = () => "../s-seckill-block/s-seckill-block.js";
const _easycom_s_point_block = () => "../s-point-block/s-point-block.js";
const _easycom_s_live_block = () => "../s-live-block/s-live-block.js";
const _easycom_s_coupon_block = () => "../s-coupon-block/s-coupon-block.js";
const _easycom_s_richtext_block = () => "../s-richtext-block/s-richtext-block.js";
const _easycom_s_user_card = () => "../s-user-card/s-user-card.js";
const _easycom_s_order_card = () => "../s-order-card/s-order-card.js";
const _easycom_s_wallet_card = () => "../s-wallet-card/s-wallet-card.js";
const _easycom_s_coupon_card = () => "../s-coupon-card/s-coupon-card.js";
if (!Math) {
  (_easycom_s_search_block + _easycom_s_notice_block + _easycom_s_menu_button + _easycom_s_menu_list + _easycom_s_menu_grid + _easycom_s_popup_image + _easycom_s_float_menu + _easycom_s_image_block + _easycom_s_image_banner + _easycom_s_title_block + _easycom_s_image_cube + _easycom_s_video_block + _easycom_s_line_block + _easycom_s_hotzone_block + _easycom_s_goods_card + _easycom_s_goods_shelves + _easycom_s_groupon_block + _easycom_s_seckill_block + _easycom_s_point_block + _easycom_s_live_block + _easycom_s_coupon_block + _easycom_s_richtext_block + _easycom_s_user_card + _easycom_s_order_card + _easycom_s_wallet_card + _easycom_s_coupon_card)();
}
const _sfc_main = {
  __name: "s-block-item",
  props: {
    type: {
      type: String,
      default: ""
    },
    data: {
      type: Object,
      default() {
      }
    },
    styles: {
      type: Object,
      default() {
      }
    }
  },
  setup(__props) {
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.type === "SearchBar"
      }, __props.type === "SearchBar" ? {
        b: common_vendor.p({
          data: __props.data,
          styles: __props.styles,
          navbar: false
        })
      } : {}, {
        c: __props.type === "NoticeBar"
      }, __props.type === "NoticeBar" ? {
        d: common_vendor.p({
          data: __props.data
        })
      } : {}, {
        e: __props.type === "MenuSwiper"
      }, __props.type === "MenuSwiper" ? {
        f: common_vendor.p({
          data: __props.data,
          styles: __props.styles
        })
      } : {}, {
        g: __props.type === "MenuList"
      }, __props.type === "MenuList" ? {
        h: common_vendor.p({
          data: __props.data
        })
      } : {}, {
        i: __props.type === "MenuGrid"
      }, __props.type === "MenuGrid" ? {
        j: common_vendor.p({
          data: __props.data,
          styles: __props.styles
        })
      } : {}, {
        k: __props.type === "Popover"
      }, __props.type === "Popover" ? {
        l: common_vendor.p({
          data: __props.data
        })
      } : {}, {
        m: __props.type === "FloatingActionButton"
      }, __props.type === "FloatingActionButton" ? {
        n: common_vendor.p({
          data: __props.data
        })
      } : {}, {
        o: __props.type === "ImageBar"
      }, __props.type === "ImageBar" ? {
        p: common_vendor.p({
          data: __props.data,
          styles: __props.styles
        })
      } : {}, {
        q: __props.type === "Carousel"
      }, __props.type === "Carousel" ? {
        r: common_vendor.p({
          data: __props.data,
          styles: __props.styles
        })
      } : {}, {
        s: __props.type === "TitleBar"
      }, __props.type === "TitleBar" ? {
        t: common_vendor.p({
          data: __props.data,
          styles: __props.styles
        })
      } : {}, {
        v: __props.type === "MagicCube"
      }, __props.type === "MagicCube" ? {
        w: common_vendor.p({
          data: __props.data,
          styles: __props.styles
        })
      } : {}, {
        x: __props.type === "VideoPlayer"
      }, __props.type === "VideoPlayer" ? {
        y: common_vendor.p({
          data: __props.data,
          styles: __props.styles
        })
      } : {}, {
        z: __props.type === "Divider"
      }, __props.type === "Divider" ? {
        A: common_vendor.p({
          data: __props.data
        })
      } : {}, {
        B: __props.type === "HotZone"
      }, __props.type === "HotZone" ? {
        C: common_vendor.p({
          data: __props.data,
          styles: __props.styles
        })
      } : {}, {
        D: __props.type === "ProductCard"
      }, __props.type === "ProductCard" ? {
        E: common_vendor.p({
          data: __props.data,
          styles: __props.styles
        })
      } : {}, {
        F: __props.type === "ProductList"
      }, __props.type === "ProductList" ? {
        G: common_vendor.p({
          data: __props.data,
          styles: __props.styles
        })
      } : {}, {
        H: __props.type === "PromotionCombination"
      }, __props.type === "PromotionCombination" ? {
        I: common_vendor.p({
          data: __props.data,
          styles: __props.styles
        })
      } : {}, {
        J: __props.type === "PromotionSeckill"
      }, __props.type === "PromotionSeckill" ? {
        K: common_vendor.p({
          data: __props.data,
          styles: __props.styles
        })
      } : {}, {
        L: __props.type === "PromotionPoint"
      }, __props.type === "PromotionPoint" ? {
        M: common_vendor.p({
          data: __props.data,
          styles: __props.styles
        })
      } : {}, {
        N: __props.type === "MpLive"
      }, __props.type === "MpLive" ? {
        O: common_vendor.p({
          data: __props.data,
          styles: __props.styles
        })
      } : {}, {
        P: __props.type === "CouponCard"
      }, __props.type === "CouponCard" ? {
        Q: common_vendor.p({
          data: __props.data,
          styles: __props.styles
        })
      } : {}, {
        R: __props.type === "PromotionArticle"
      }, __props.type === "PromotionArticle" ? {
        S: common_vendor.p({
          data: __props.data,
          styles: __props.styles
        })
      } : {}, {
        T: __props.type === "UserCard"
      }, __props.type === "UserCard" ? {
        U: common_vendor.p({
          data: __props.data,
          styles: __props.styles
        })
      } : {}, {
        V: __props.type === "UserOrder"
      }, __props.type === "UserOrder" ? {
        W: common_vendor.p({
          data: __props.data,
          styles: __props.styles
        })
      } : {}, {
        X: __props.type === "UserWallet"
      }, __props.type === "UserWallet" ? {
        Y: common_vendor.p({
          data: __props.data,
          styles: __props.styles
        })
      } : {}, {
        Z: __props.type === "UserCoupon"
      }, __props.type === "UserCoupon" ? {
        aa: common_vendor.p({
          data: __props.data,
          styles: __props.styles
        })
      } : {});
    };
  }
};
wx.createComponent(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/sheep/components/s-block-item/s-block-item.js.map
