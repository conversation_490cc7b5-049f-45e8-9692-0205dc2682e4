/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-numbox.data-v-7ae2ee72 {
  display: flex;
  flex-direction: row;
}
.uni-numbox-btns.data-v-7ae2ee72 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
  background-color: #f5f5f5;
}
.uni-numbox__value.data-v-7ae2ee72 {
  margin: 0 2px;
  background-color: #f5f5f5;
  width: 40px;
  height: 26px;
  text-align: center;
  font-size: 14px;
  border-left-width: 0;
  border-right-width: 0;
  color: #333;
}
.uni-numbox__minus.data-v-7ae2ee72 {
  border-top-left-radius: 2px;
  border-bottom-left-radius: 2px;
}
.uni-numbox__plus.data-v-7ae2ee72 {
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
}
.uni-numbox--text.data-v-7ae2ee72 {
  line-height: 20px;
  font-size: 20px;
  font-weight: 300;
  color: #333;
}
.uni-numbox .uni-numbox--disabled.data-v-7ae2ee72 {
  color: #c0c0c0 !important;
}