<view class=""><view class="{{['uni-forms-item', n && 'uni-forms-item--border', o && 'is-first-border', p && 'uni-forms-item-error']}}"><view class="uni-forms-item__box"><view class="{{['uni-forms-item__inner', i]}}"><view class="uni-forms-item__label" style="{{'width:' + f + ';' + ('justify-content:' + g)}}"><block wx:if="{{$slots.label}}"><slot name="label"></slot></block><block wx:else><text wx:if="{{a}}" class="is-required">*</text><uni-icons wx:if="{{b}}" class="label-icon" u-i="57e41e18-0" bind:__l="__l" u-p="{{c}}"/><text class="label-text">{{d}}</text><view wx:if="{{e}}" class="label-seat"></view></block></view><view class="{{['uni-forms-item__content', h && 'is-input-error-border']}}"><slot></slot></view></view><view wx:if="{{j}}" class="{{['uni-error-message', l && 'uni-error-msg--boeder']}}" style="{{'padding-left:' + m}}"><text class="uni-error-message-text">{{k}}</text></view></view></view></view>