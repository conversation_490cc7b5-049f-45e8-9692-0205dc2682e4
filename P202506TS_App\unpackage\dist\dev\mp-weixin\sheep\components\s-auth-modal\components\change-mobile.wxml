<view class="data-v-146b49fd"><view class="head-box ss-m-b-60 data-v-146b49fd"><view class="head-title ss-m-b-20 data-v-146b49fd">{{a}}</view><view class="head-subtitle data-v-146b49fd">为了您的账号安全，请使用本人手机号码</view></view><uni-forms wx:if="{{o}}" class="r data-v-146b49fd" u-s="{{['d']}}" u-r="changeMobileRef" u-i="146b49fd-0" bind:__l="__l" bindupdateModelValue="{{n}}" u-p="{{o}}"><uni-forms-item wx:if="{{h}}" class="data-v-146b49fd" u-s="{{['d']}}" u-i="146b49fd-1,146b49fd-0" bind:__l="__l" u-p="{{h}}"><uni-easyinput wx:if="{{g}}" class="data-v-146b49fd" u-s="{{['right']}}" u-i="146b49fd-2,146b49fd-1" bind:__l="__l" bindupdateModelValue="{{f}}" u-p="{{g}}"><button disabled="{{c}}" class="{{['ss-reset-button', 'code-btn-start', 'data-v-146b49fd', d && 'code-btn-end']}}" bindtap="{{e}}" slot="right">{{b}}</button></uni-easyinput></uni-forms-item><uni-forms-item wx:if="{{l}}" class="data-v-146b49fd" u-s="{{['d']}}" u-i="146b49fd-3,146b49fd-0" bind:__l="__l" u-p="{{l}}"><uni-easyinput wx:if="{{k}}" class="data-v-146b49fd" u-s="{{['right']}}" u-i="146b49fd-4,146b49fd-3" bind:__l="__l" bindupdateModelValue="{{j}}" u-p="{{k}}"><button class="ss-reset-button login-btn-start data-v-146b49fd" bindtap="{{i}}" slot="right"> 确认 </button></uni-easyinput></uni-forms-item></uni-forms><button wx:if="{{p}}" class="ss-reset-button type-btn data-v-146b49fd" open-type="getPhoneNumber" bindgetphonenumber="{{q}}"> 使用微信手机号 </button></view>