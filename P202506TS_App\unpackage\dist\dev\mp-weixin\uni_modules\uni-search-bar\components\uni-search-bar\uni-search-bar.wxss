/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-searchbar {
  display: flex;
  flex-direction: row;
  position: relative;
  padding: 5px;
}
.uni-searchbar__box {
  display: flex;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
  flex: 1;
  flex-direction: row;
  align-items: center;
  height: 36px;
  padding: 5px 8px 5px 0px;
}
.uni-searchbar__box-icon-search {
  display: flex;
  flex-direction: row;
  padding: 0 8px;
  justify-content: center;
  align-items: center;
  color: #B3B3B3;
}
.uni-searchbar__box-search-input {
  flex: 1;
  font-size: 14px;
  color: #333;
}
.uni-searchbar__box-icon-clear {
  align-items: center;
  line-height: 24px;
  padding-left: 8px;
}
.uni-searchbar__text-placeholder {
  font-size: 14px;
  color: #B3B3B3;
  margin-left: 5px;
}
.uni-searchbar__cancel {
  padding-left: 10px;
  line-height: 36px;
  font-size: 14px;
  color: #333333;
}