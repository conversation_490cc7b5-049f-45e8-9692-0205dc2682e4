/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-load-more {
  display: flex;
  flex-direction: row;
  height: 40px;
  align-items: center;
  justify-content: center;
}
.uni-load-more__text {
  font-size: 14px;
  margin-left: 8px;
}
.uni-load-more__img {
  width: 24px;
  height: 24px;
}
.uni-load-more__img--nvue {
  color: #666666;
}
.uni-load-more__img--android,
.uni-load-more__img--ios {
  width: 24px;
  height: 24px;
  transform: rotate(0deg);
}
.uni-load-more__img--android {
  animation: loading-ios 1s 0s linear infinite;
}
@keyframes loading-android {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.uni-load-more__img--ios-H5 {
  position: relative;
  animation: loading-ios-H5 1s 0s step-end infinite;
}
.uni-load-more__img--ios-H5 image {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
@keyframes loading-ios-H5 {
0% {
    transform: rotate(0deg);
}
8% {
    transform: rotate(30deg);
}
16% {
    transform: rotate(60deg);
}
24% {
    transform: rotate(90deg);
}
32% {
    transform: rotate(120deg);
}
40% {
    transform: rotate(150deg);
}
48% {
    transform: rotate(180deg);
}
56% {
    transform: rotate(210deg);
}
64% {
    transform: rotate(240deg);
}
73% {
    transform: rotate(270deg);
}
82% {
    transform: rotate(300deg);
}
91% {
    transform: rotate(330deg);
}
100% {
    transform: rotate(360deg);
}
}
.uni-load-more__img--android-MP {
  position: relative;
  width: 24px;
  height: 24px;
  transform: rotate(0deg);
  animation: loading-ios 1s 0s ease infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: solid 2px transparent;
  border-top: solid 2px #777777;
  transform-origin: center;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon:nth-child(1) {
  animation: loading-android-MP-1 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon:nth-child(2) {
  animation: loading-android-MP-2 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon:nth-child(3) {
  animation: loading-android-MP-3 1s 0s linear infinite;
}
@keyframes loading-android {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-1 {
0% {
    transform: rotate(0deg);
}
50% {
    transform: rotate(90deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-2 {
0% {
    transform: rotate(0deg);
}
50% {
    transform: rotate(180deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-3 {
0% {
    transform: rotate(0deg);
}
50% {
    transform: rotate(270deg);
}
100% {
    transform: rotate(360deg);
}
}