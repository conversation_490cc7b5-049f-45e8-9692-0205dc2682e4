/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-swiper__warp.data-v-0667e3db {
  display: flex;
  flex: 1;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}
.uni-swiper__dots-box.data-v-0667e3db {
  position: absolute;
  bottom: 10px;
  left: 0;
  right: 0;
  display: flex;
  flex: 1;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.uni-swiper__dots-item.data-v-0667e3db {
  width: 8px;
  border-radius: 100px;
  margin-left: 6px;
  background-color: rgba(0, 0, 0, 0.4);
  cursor: pointer;
}
.uni-swiper__dots-item.data-v-0667e3db:first-child {
  margin: 0;
}
.uni-swiper__dots-default.data-v-0667e3db {
  border-radius: 100px;
}
.uni-swiper__dots-long.data-v-0667e3db {
  border-radius: 50px;
}
.uni-swiper__dots-bar.data-v-0667e3db {
  border-radius: 50px;
}
.uni-swiper__dots-nav.data-v-0667e3db {
  bottom: 0px;
  padding: 8px 0;
  display: flex;
  flex: 1;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.2);
}
.uni-swiper__dots-nav-item.data-v-0667e3db {
  /* overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; */
  font-size: 14px;
  color: #fff;
  margin: 0 15px;
}
.uni-swiper__dots-indexes.data-v-0667e3db {
  display: flex;
  justify-content: center;
  align-items: center;
}
.uni-swiper__dots-indexes-text.data-v-0667e3db {
  color: #fff;
  font-size: 12px;
  line-height: 14px;
}