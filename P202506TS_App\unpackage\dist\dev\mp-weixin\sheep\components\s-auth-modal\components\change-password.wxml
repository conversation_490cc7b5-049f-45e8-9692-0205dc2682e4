<view class="data-v-639aa0c2"><view class="head-box ss-m-b-60 data-v-639aa0c2"><view class="head-title ss-m-b-20 data-v-639aa0c2">修改密码</view><view class="head-subtitle data-v-639aa0c2">如密码丢失或未设置,请点击忘记密码重新设置</view></view><uni-forms wx:if="{{n}}" class="r data-v-639aa0c2" u-s="{{['d']}}" u-r="changePasswordRef" u-i="639aa0c2-0" bind:__l="__l" bindupdateModelValue="{{m}}" u-p="{{n}}"><uni-forms-item wx:if="{{g}}" class="data-v-639aa0c2" u-s="{{['d']}}" u-i="639aa0c2-1,639aa0c2-0" bind:__l="__l" u-p="{{g}}"><uni-easyinput wx:if="{{f}}" class="data-v-639aa0c2" u-s="{{['right']}}" u-i="639aa0c2-2,639aa0c2-1" bind:__l="__l" bindupdateModelValue="{{e}}" u-p="{{f}}"><button disabled="{{b}}" class="{{['ss-reset-button', 'code-btn', 'code-btn-start', 'data-v-639aa0c2', c && 'code-btn-end']}}" bindtap="{{d}}" slot="right">{{a}}</button></uni-easyinput></uni-forms-item><uni-forms-item wx:if="{{k}}" class="data-v-639aa0c2" u-s="{{['d']}}" u-i="639aa0c2-3,639aa0c2-0" bind:__l="__l" u-p="{{k}}"><uni-easyinput wx:if="{{j}}" class="data-v-639aa0c2" u-s="{{['right']}}" u-i="639aa0c2-4,639aa0c2-3" bind:__l="__l" bindupdateModelValue="{{i}}" u-p="{{j}}"><button class="ss-reset-button login-btn-start data-v-639aa0c2" bindtap="{{h}}" slot="right"> 确认 </button></uni-easyinput></uni-forms-item></uni-forms><button class="ss-reset-button type-btn data-v-639aa0c2" bindtap="{{o}}"> 取消修改 </button></view>