<view class="uni-swiper__warp data-v-0667e3db"><slot/><view wx:if="{{a}}" style="{{'bottom:' + d}}" class="uni-swiper__dots-box data-v-0667e3db" key="default"><view wx:for="{{b}}" wx:for-item="item" wx:key="d" bindtap="{{item.a}}" style="{{'width:' + item.b + ';' + ('height:' + c) + ';' + ('background-color:' + item.c) + ';' + ('border-radius:' + '0px')}}" class="uni-swiper__dots-item uni-swiper__dots-bar data-v-0667e3db"/></view><view wx:if="{{e}}" style="{{'bottom:' + i}}" class="uni-swiper__dots-box data-v-0667e3db" key="dot"><view wx:for="{{f}}" wx:for-item="item" wx:key="d" bindtap="{{item.a}}" style="{{'width:' + g + ';' + ('height:' + h) + ';' + ('background-color:' + item.b) + ';' + ('border:' + item.c)}}" class="uni-swiper__dots-item data-v-0667e3db"/></view><view wx:if="{{j}}" style="{{'bottom:' + m}}" class="uni-swiper__dots-box data-v-0667e3db" key="round"><view wx:for="{{k}}" wx:for-item="item" wx:key="f" bindtap="{{item.a}}" class="{{[item.b, 'uni-swiper__dots-item', 'data-v-0667e3db']}}" style="{{'width:' + item.c + ';' + ('height:' + l) + ';' + ('background-color:' + item.d) + ';' + ('border:' + item.e)}}"/></view><view wx:if="{{n}}" key="nav" style="{{'background-color:' + q + ';' + ('bottom:' + '0')}}" class="uni-swiper__dots-box uni-swiper__dots-nav data-v-0667e3db"><text style="{{'color:' + p}}" class="uni-swiper__dots-nav-item data-v-0667e3db">{{o}}</text></view><view wx:if="{{r}}" key="indexes" style="{{'bottom:' + w}}" class="uni-swiper__dots-box data-v-0667e3db"><view wx:for="{{s}}" wx:for-item="item" wx:key="f" bindtap="{{item.b}}" style="{{'width:' + t + ';' + ('height:' + v) + ';' + ('color:' + item.c) + ';' + ('background-color:' + item.d) + ';' + ('border:' + item.e)}}" class="uni-swiper__dots-item uni-swiper__dots-indexes data-v-0667e3db"><text class="uni-swiper__dots-indexes-text data-v-0667e3db">{{item.a}}</text></view></view></view>