/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.info-bg-color.data-v-81b7b3dc {
  background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
}
.disabled-bg-color.data-v-81b7b3dc {
  background: #999;
}
.info-color.data-v-81b7b3dc {
  color: #333;
}
.subtitle-color.data-v-81b7b3dc {
  color: #666;
}
.disabled-color.data-v-81b7b3dc {
  color: #999;
}
.content.data-v-81b7b3dc {
  width: 100%;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  -webkit-mask: radial-gradient(circle at 12rpx 100%, #0000 12rpx, red 0) -12rpx;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.04);
}
.content .tag.data-v-81b7b3dc {
  width: 100rpx;
  color: #fff;
  height: 40rpx;
  font-size: 24rpx;
  border-radius: 20rpx 0 20rpx 0;
}
.content .title.data-v-81b7b3dc {
  padding-bottom: 22rpx;
  border-bottom: 2rpx dashed #d3d3d3;
}
.content .title .value-text.data-v-81b7b3dc {
  font-size: 32rpx;
  font-weight: 600;
}
.content .title .sellby-text.data-v-81b7b3dc {
  font-size: 24rpx;
  font-weight: 400;
}
.content .title .value-price.data-v-81b7b3dc {
  font-size: 64rpx;
  font-weight: 500;
  line-height: normal;
  font-family: OPPOSANS;
}
.content .title .value-reduce.data-v-81b7b3dc {
  line-height: normal;
  font-size: 32rpx;
}
.content .title .value-discount.data-v-81b7b3dc {
  line-height: normal;
  font-size: 28rpx;
}
.content .title .value-enough.data-v-81b7b3dc {
  font-size: 24rpx;
  font-weight: 400;
  font-family: OPPOSANS;
}
.desc.data-v-81b7b3dc {
  width: 100%;
  background: #fff;
  -webkit-mask: radial-gradient(circle at 12rpx 0%, #0000 12rpx, red 0) -12rpx;
  box-shadow: rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
  padding: 24rpx 30rpx;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.04);
  border-radius: 0 0 20rpx 20rpx;
}
.desc .desc-title.data-v-81b7b3dc {
  font-size: 24rpx;
  color: #999;
  font-weight: 400;
}
.price-text.data-v-81b7b3dc {
  color: #ff0000;
}