/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.address-item.data-v-bb7deb2b {
  padding: 24rpx 30rpx;
}
.address-item .item-left.data-v-bb7deb2b {
  width: 600rpx;
}
.address-item .area-text.data-v-bb7deb2b {
  font-size: 26rpx;
  font-weight: 400;
  color: #999999;
}
.address-item .address-text.data-v-bb7deb2b {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  line-height: 48rpx;
}
.address-item .person-text.data-v-bb7deb2b {
  font-size: 28rpx;
  font-weight: 400;
  color: #999999;
}
.edit-btn.data-v-bb7deb2b {
  width: 44rpx;
  height: 44rpx;
  background: #f8f9fa;
  border-radius: 50%;
}
.edit-btn .edit-icon.data-v-bb7deb2b {
  width: 24rpx;
  height: 24rpx;
}
image.data-v-bb7deb2b {
  width: 100%;
  height: 100%;
}