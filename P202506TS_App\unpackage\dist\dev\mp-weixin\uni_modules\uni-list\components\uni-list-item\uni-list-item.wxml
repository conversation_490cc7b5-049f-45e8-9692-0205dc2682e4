<view class="{{[C && 'uni-list-item--disabled', 'uni-list-item']}}" hover-class="{{D}}" bindtap="{{E}}"><view wx:if="{{a}}" class="{{['border--left', b && 'uni-list--border']}}"></view><view class="{{['uni-list-item__container', y && 'container--right', z && 'flex--direction']}}"><block wx:if="{{$slots.header}}"><slot name="header"></slot></block><block wx:else><view class="uni-list-item__header"><view wx:if="{{c}}" class="uni-list-item__icon"><image src="{{d}}" class="{{['uni-list-item__icon-img', e]}}"/></view><view wx:elif="{{f}}" class="uni-list-item__icon"><uni-icons wx:if="{{g}}" u-i="731c2536-0" bind:__l="__l" u-p="{{g}}"/></view></view></block><block wx:if="{{$slots.body}}"><slot name="body"></slot></block><block wx:else><view class="{{['uni-list-item__content', m && 'uni-list-item__content--center']}}"><text wx:if="{{h}}" class="{{['uni-list-item__content-title', j]}}">{{i}}</text><text wx:if="{{k}}" class="uni-list-item__content-note">{{l}}</text></view></block><block wx:if="{{$slots.footer}}"><slot name="footer"></slot></block><block wx:else><view wx:if="{{n}}" class="{{['uni-list-item__extra', x && 'flex--justify']}}"><text wx:if="{{o}}" class="uni-list-item__extra-text">{{p}}</text><uni-badge wx:if="{{q}}" u-i="731c2536-1" bind:__l="__l" u-p="{{r}}"/><switch wx:if="{{s}}" disabled="{{t}}" checked="{{v}}" bindchange="{{w}}"/></view></block></view><uni-icons wx:if="{{A}}" class="uni-icon-wrapper" u-i="731c2536-2" bind:__l="__l" u-p="{{B}}"/></view>