/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.horizontal-margin {
  margin-right: 20rpx;
}
.vertical-margin {
  margin-bottom: 20rpx;
}
.uni-fab {
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 12;
  border-radius: 45px;
}
.uni-fab--active {
  opacity: 1;
}
.uni-fab--leftBottom {
  left: 15px;
  bottom: 30px;
}
.uni-fab--leftTop {
  left: 15px;
  top: 30px;
}
.uni-fab--rightBottom {
  right: 24rpx;
  bottom: calc(100rpx + env(safe-area-inset-bottom));
}
.uni-fab--rightTop {
  right: 15px;
  top: 30px;
}
.uni-fab__circle {
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 64rpx;
  height: 64rpx;
  background-color: #3c3e49;
  border-radius: 50%;
  z-index: 13;
}
.uni-fab__circle--leftBottom {
  left: 15px;
  bottom: 30px;
}
.uni-fab__circle--leftTop {
  left: 15px;
  top: 30px;
}
.uni-fab__circle--rightBottom {
  right: 40rpx;
  bottom: calc(120rpx + env(safe-area-inset-bottom));
}
.uni-fab__circle--rightTop {
  right: 15px;
  top: 30px;
}
.uni-fab__circle--left {
  left: 0;
}
.uni-fab__circle--right {
  right: 0;
}
.uni-fab__circle--top {
  top: 0;
}
.uni-fab__circle--bottom {
  bottom: 0;
}
.uni-fab__plus {
  font-weight: bold;
}
.fab-circle-icon {
  transform: rotate(0deg);
  transition: transform 0.3s;
  font-weight: 200;
}
.uni-fab__plus--active {
  transform: rotate(135deg);
}
.uni-fab__content {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  border-radius: 55px;
  overflow: hidden;
  transition-property: width, height;
  transition-duration: 0.2s;
  width: 64rpx;
  border-color: #dddddd;
  border-width: 1rpx;
  border-style: solid;
}
.uni-fab__content--other-platform {
  border-width: 0px;
}
.uni-fab__content--left {
  justify-content: flex-start;
}
.uni-fab__content--right {
  justify-content: flex-end;
}
.uni-fab__content--flexDirection {
  flex-direction: column;
  justify-content: flex-end;
}
.uni-fab__content--flexDirectionStart {
  flex-direction: column;
  justify-content: flex-start;
}
.uni-fab__content--flexDirectionEnd {
  flex-direction: column;
  justify-content: flex-end;
}
.uni-fab__item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100rpx;
  opacity: 0;
  transition: opacity 0.2s;
}
.uni-fab__item--active {
  opacity: 1;
}
.uni-fab__item-image {
  width: 52rpx;
  height: 52rpx;
}
.uni-fab__item-text {
  color: #ffffff;
  display: table;
  font-size: 24rpx;
  margin-top: 4px;
}
.uni-fab__item--first {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 0 !important;
}