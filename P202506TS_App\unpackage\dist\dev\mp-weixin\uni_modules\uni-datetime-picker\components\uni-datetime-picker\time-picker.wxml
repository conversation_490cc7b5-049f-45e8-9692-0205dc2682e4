<view class="uni-datetime-picker"><view bindtap="{{f}}"><block wx:if="{{$slots.d}}"><slot></slot></block><block wx:else><view class="{{['uni-datetime-picker-timebox-pointer', d && 'uni-datetime-picker-disabled', e && 'uni-datetime-picker-timebox']}}"><text class="uni-datetime-picker-text">{{a}}</text><view wx:if="{{b}}" class="uni-datetime-picker-time"><text class="uni-datetime-picker-text">{{c}}</text></view></view></block></view><view wx:if="{{g}}" id="mask" class="uni-datetime-picker-mask" bindtap="{{h}}"></view><view wx:if="{{i}}" class="{{['uni-datetime-picker-popup', J]}}" style="{{K}}"><view class="uni-title"><text class="uni-datetime-picker-text">{{j}}</text></view><view wx:if="{{k}}" class="uni-datetime-picker__container-box"><block wx:if="{{r0}}"><picker-view class="uni-datetime-picker-view" indicator-style="{{o}}" value="{{p}}" bindchange="{{q}}"><picker-view-column><view wx:for="{{l}}" wx:for-item="item" wx:key="b" class="uni-datetime-picker-item"><text class="uni-datetime-picker-item">{{item.a}}</text></view></picker-view-column><picker-view-column><view wx:for="{{m}}" wx:for-item="item" wx:key="b" class="uni-datetime-picker-item"><text class="uni-datetime-picker-item">{{item.a}}</text></view></picker-view-column><picker-view-column><view wx:for="{{n}}" wx:for-item="item" wx:key="b" class="uni-datetime-picker-item"><text class="uni-datetime-picker-item">{{item.a}}</text></view></picker-view-column></picker-view></block><text class="uni-datetime-picker-sign sign-left">-</text><text class="uni-datetime-picker-sign sign-right">-</text></view><view wx:if="{{r}}" class="uni-datetime-picker__container-box"><block wx:if="{{r0}}"><picker-view class="{{['uni-datetime-picker-view', x]}}" indicator-style="{{y}}" value="{{z}}" bindchange="{{A}}"><picker-view-column><view wx:for="{{s}}" wx:for-item="item" wx:key="b" class="uni-datetime-picker-item"><text class="uni-datetime-picker-item">{{item.a}}</text></view></picker-view-column><picker-view-column><view wx:for="{{t}}" wx:for-item="item" wx:key="b" class="uni-datetime-picker-item"><text class="uni-datetime-picker-item">{{item.a}}</text></view></picker-view-column><picker-view-column wx:if="{{v}}"><view wx:for="{{w}}" wx:for-item="item" wx:key="b" class="uni-datetime-picker-item"><text class="uni-datetime-picker-item">{{item.a}}</text></view></picker-view-column></picker-view></block><text class="{{['uni-datetime-picker-sign', B]}}">:</text><text wx:if="{{C}}" class="uni-datetime-picker-sign sign-right">:</text></view><view class="uni-datetime-picker-btn"><view bindtap="{{E}}"><text class="uni-datetime-picker-btn-text">{{D}}</text></view><view class="uni-datetime-picker-btn-group"><view class="uni-datetime-picker-cancel" bindtap="{{G}}"><text class="uni-datetime-picker-btn-text">{{F}}</text></view><view bindtap="{{I}}"><text class="uni-datetime-picker-btn-text">{{H}}</text></view></view></view></view></view>