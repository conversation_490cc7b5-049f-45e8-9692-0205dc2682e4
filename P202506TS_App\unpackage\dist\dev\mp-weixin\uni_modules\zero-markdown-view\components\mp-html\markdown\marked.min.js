"use strict";
const common_vendor = require("../../../../../common/vendor.js");
/*!
 * marked - a markdown parser
 * Copyright (c) 2011-2020, <PERSON>. (MIT Licensed)
 * https://github.com/markedjs/marked
 */
function t() {
  function i(e2, t3) {
    for (var n2 = 0; n2 < t3.length; n2++) {
      var r2 = t3[n2];
      r2.enumerable = r2.enumerable || false, r2.configurable = true, "value" in r2 && (r2.writable = true), Object.defineProperty(e2, r2.key, r2);
    }
  }
  function s(e2, t3) {
    (null == t3 || t3 > e2.length) && (t3 = e2.length);
    for (var n2 = 0, r2 = new Array(t3); n2 < t3; n2++)
      r2[n2] = e2[n2];
    return r2;
  }
  function p(e2, t3) {
    var n2;
    if ("undefined" != typeof Symbol && null != e2[Symbol.iterator])
      return (n2 = e2[Symbol.iterator]()).next.bind(n2);
    if (Array.isArray(e2) || (n2 = function(e3, t4) {
      if (e3) {
        if ("string" == typeof e3)
          return s(e3, t4);
        var n3 = Object.prototype.toString.call(e3).slice(8, -1);
        return "Object" === n3 && e3.constructor && (n3 = e3.constructor.name), "Map" === n3 || "Set" === n3 ? Array.from(e3) : "Arguments" === n3 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n3) ? s(e3, t4) : void 0;
      }
    }(e2)) || t3 && e2 && "number" == typeof e2.length) {
      n2 && (e2 = n2);
      var r2 = 0;
      return function() {
        return r2 >= e2.length ? { done: true } : { done: false, value: e2[r2++] };
      };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }
  function n(e2) {
    return c[e2];
  }
  var e, t2 = (function(t3) {
    function e2() {
      return { baseUrl: null, breaks: false, gfm: true, headerIds: true, headerPrefix: "", highlight: null, langPrefix: "language-", mangle: true, pedantic: false, renderer: null, sanitize: false, sanitizer: null, silent: false, smartLists: false, smartypants: false, tokenizer: null, walkTokens: null, xhtml: false };
    }
    t3.exports = { defaults: e2(), getDefaults: e2, changeDefaults: function(e3) {
      t3.exports.defaults = e3;
    } };
  }(e = { exports: {} }), e.exports), r = (t2.defaults, t2.getDefaults, t2.changeDefaults, /[&<>"']/), l = /[&<>"']/g, a = /[<>"']|&(?!#?\w+;)/, o = /[<>"']|&(?!#?\w+;)/g, c = { "&": "&amp;", "<": "&lt;", ">": "&gt;", '"': "&quot;", "'": "&#39;" };
  var u = /&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;
  function h(e2) {
    return e2.replace(u, function(e3, t3) {
      return "colon" === (t3 = t3.toLowerCase()) ? ":" : "#" === t3.charAt(0) ? "x" === t3.charAt(1) ? String.fromCharCode(parseInt(t3.substring(2), 16)) : String.fromCharCode(+t3.substring(1)) : "";
    });
  }
  var g = /(^|[^\[])\^/g;
  var f = /[^\w:]/g, d = /^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;
  var k = {}, b = /^[^:]+:\/*[^/]*$/, m = /^([^:]+:)[\s\S]*$/, x = /^([^:]+:\/*[^/]*)[\s\S]*$/;
  function w(e2, t3) {
    k[" " + e2] || (b.test(e2) ? k[" " + e2] = e2 + "/" : k[" " + e2] = v(e2, "/", true));
    var n2 = -1 === (e2 = k[" " + e2]).indexOf(":");
    return "//" === t3.substring(0, 2) ? n2 ? t3 : e2.replace(m, "$1") + t3 : "/" === t3.charAt(0) ? n2 ? t3 : e2.replace(x, "$1") + t3 : e2 + t3;
  }
  function v(e2, t3, n2) {
    var r2 = e2.length;
    if (0 === r2)
      return "";
    for (var i2 = 0; i2 < r2; ) {
      var s2 = e2.charAt(r2 - i2 - 1);
      if (s2 !== t3 || n2) {
        if (s2 === t3 || !n2)
          break;
        i2++;
      } else
        i2++;
    }
    return e2.substr(0, r2 - i2);
  }
  var _ = function(e2, t3) {
    if (t3) {
      if (r.test(e2))
        return e2.replace(l, n);
    } else if (a.test(e2))
      return e2.replace(o, n);
    return e2;
  }, y = h, z = function(n2, e2) {
    n2 = n2.source || n2, e2 = e2 || "";
    var r2 = { replace: function(e3, t3) {
      return t3 = (t3 = t3.source || t3).replace(g, "$1"), n2 = n2.replace(e3, t3), r2;
    }, getRegex: function() {
      return new RegExp(n2, e2);
    } };
    return r2;
  }, S = function(e2, t3, n2) {
    if (e2) {
      var r2;
      try {
        r2 = decodeURIComponent(h(n2)).replace(f, "").toLowerCase();
      } catch (e3) {
        return null;
      }
      if (0 === r2.indexOf("javascript:") || 0 === r2.indexOf("vbscript:") || 0 === r2.indexOf("data:"))
        return null;
    }
    t3 && !d.test(n2) && (n2 = w(t3, n2));
    try {
      n2 = encodeURI(n2).replace(/%25/g, "%");
    } catch (e3) {
      return null;
    }
    return n2;
  }, $ = { exec: function() {
  } }, A = function(e2) {
    for (var t3, n2, r2 = 1; r2 < arguments.length; r2++)
      for (n2 in t3 = arguments[r2])
        Object.prototype.hasOwnProperty.call(t3, n2) && (e2[n2] = t3[n2]);
    return e2;
  }, R = function(e2, t3) {
    var n2 = e2.replace(/\|/g, function(e3, t4, n3) {
      for (var r3 = false, i2 = t4; 0 <= --i2 && "\\" === n3[i2]; )
        r3 = !r3;
      return r3 ? "|" : " |";
    }).split(/ \|/), r2 = 0;
    if (n2.length > t3)
      n2.splice(t3);
    else
      for (; n2.length < t3; )
        n2.push("");
    for (; r2 < n2.length; r2++)
      n2[r2] = n2[r2].trim().replace(/\\\|/g, "|");
    return n2;
  }, T = function(e2, t3) {
    if (-1 === e2.indexOf(t3[1]))
      return -1;
    for (var n2 = e2.length, r2 = 0, i2 = 0; i2 < n2; i2++)
      if ("\\" === e2[i2])
        i2++;
      else if (e2[i2] === t3[0])
        r2++;
      else if (e2[i2] === t3[1] && --r2 < 0)
        return i2;
    return -1;
  }, I = function(e2) {
    e2 && e2.sanitize && !e2.silent && common_vendor.index.__f__("warn", "at uni_modules/zero-markdown-view/components/mp-html/markdown/marked.min.js:6", "marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options");
  }, Z = function(e2, t3) {
    if (t3 < 1)
      return "";
    for (var n2 = ""; 1 < t3; )
      1 & t3 && (n2 += e2), t3 >>= 1, e2 += e2;
    return n2 + e2;
  }, q = t2.defaults, O = v, C = R, U = _, j = T;
  function E(e2, t3, n2) {
    var r2 = t3.href, i2 = t3.title ? U(t3.title) : null, t3 = e2[1].replace(/\\([\[\]])/g, "$1");
    return "!" !== e2[0].charAt(0) ? { type: "link", raw: n2, href: r2, title: i2, text: t3 } : { type: "image", raw: n2, href: r2, title: i2, text: U(t3) };
  }
  var D = function() {
    function e2(e3) {
      this.options = e3 || q;
    }
    var t3 = e2.prototype;
    return t3.space = function(e3) {
      e3 = this.rules.block.newline.exec(e3);
      if (e3)
        return 1 < e3[0].length ? { type: "space", raw: e3[0] } : { raw: "\n" };
    }, t3.code = function(e3, t4) {
      e3 = this.rules.block.code.exec(e3);
      if (e3) {
        t4 = t4[t4.length - 1];
        if (t4 && "paragraph" === t4.type)
          return { raw: e3[0], text: e3[0].trimRight() };
        t4 = e3[0].replace(/^ {4}/gm, "");
        return { type: "code", raw: e3[0], codeBlockStyle: "indented", text: this.options.pedantic ? t4 : O(t4, "\n") };
      }
    }, t3.fences = function(e3) {
      var t4 = this.rules.block.fences.exec(e3);
      if (t4) {
        var n2 = t4[0], e3 = function(e4, t5) {
          if (null === (e4 = e4.match(/^(\s+)(?:```)/)))
            return t5;
          var n3 = e4[1];
          return t5.split("\n").map(function(e5) {
            var t6 = e5.match(/^\s+/);
            return null !== t6 && t6[0].length >= n3.length ? e5.slice(n3.length) : e5;
          }).join("\n");
        }(n2, t4[3] || "");
        return { type: "code", raw: n2, lang: t4[2] && t4[2].trim(), text: e3 };
      }
    }, t3.heading = function(e3) {
      e3 = this.rules.block.heading.exec(e3);
      if (e3)
        return { type: "heading", raw: e3[0], depth: e3[1].length, text: e3[2] };
    }, t3.nptable = function(e3) {
      e3 = this.rules.block.nptable.exec(e3);
      if (e3) {
        var t4 = { type: "table", header: C(e3[1].replace(/^ *| *\| *$/g, "")), align: e3[2].replace(/^ *|\| *$/g, "").split(/ *\| */), cells: e3[3] ? e3[3].replace(/\n$/, "").split("\n") : [], raw: e3[0] };
        if (t4.header.length === t4.align.length) {
          for (var n2 = t4.align.length, r2 = 0; r2 < n2; r2++)
            /^ *-+: *$/.test(t4.align[r2]) ? t4.align[r2] = "right" : /^ *:-+: *$/.test(t4.align[r2]) ? t4.align[r2] = "center" : /^ *:-+ *$/.test(t4.align[r2]) ? t4.align[r2] = "left" : t4.align[r2] = null;
          for (n2 = t4.cells.length, r2 = 0; r2 < n2; r2++)
            t4.cells[r2] = C(t4.cells[r2], t4.header.length);
          return t4;
        }
      }
    }, t3.hr = function(e3) {
      e3 = this.rules.block.hr.exec(e3);
      if (e3)
        return { type: "hr", raw: e3[0] };
    }, t3.blockquote = function(e3) {
      var t4 = this.rules.block.blockquote.exec(e3);
      if (t4) {
        e3 = t4[0].replace(/^ *> ?/gm, "");
        return { type: "blockquote", raw: t4[0], text: e3 };
      }
    }, t3.list = function(e3) {
      e3 = this.rules.block.list.exec(e3);
      if (e3) {
        for (var t4, n2, r2, i2, s2, l2 = e3[0], a2 = e3[2], o2 = 1 < a2.length, c2 = { type: "list", raw: l2, ordered: o2, start: o2 ? +a2.slice(0, -1) : "", loose: false, items: [] }, u2 = e3[0].match(this.rules.block.item), p2 = false, h2 = u2.length, g2 = this.rules.block.listItemStart.exec(u2[0]), f2 = 0; f2 < h2; f2++) {
          if (l2 = t4 = u2[f2], f2 !== h2 - 1) {
            if ((r2 = this.rules.block.listItemStart.exec(u2[f2 + 1]))[1].length > g2[0].length || 3 < r2[1].length) {
              u2.splice(f2, 2, u2[f2] + "\n" + u2[f2 + 1]), f2--, h2--;
              continue;
            }
            (!this.options.pedantic || this.options.smartLists ? r2[2][r2[2].length - 1] !== a2[a2.length - 1] : o2 == (1 === r2[2].length)) && (n2 = u2.slice(f2 + 1).join("\n"), c2.raw = c2.raw.substring(0, c2.raw.length - n2.length), f2 = h2 - 1), g2 = r2;
          }
          r2 = t4.length, ~(t4 = t4.replace(/^ *([*+-]|\d+[.)]) ?/, "")).indexOf("\n ") && (r2 -= t4.length, t4 = this.options.pedantic ? t4.replace(/^ {1,4}/gm, "") : t4.replace(new RegExp("^ {1," + r2 + "}", "gm"), "")), r2 = p2 || /\n\n(?!\s*$)/.test(t4), f2 !== h2 - 1 && (p2 = "\n" === t4.charAt(t4.length - 1), r2 = r2 || p2), r2 && (c2.loose = true), this.options.gfm && (s2 = void 0, (i2 = /^\[[ xX]\] /.test(t4)) && (s2 = " " !== t4[1], t4 = t4.replace(/^\[[ xX]\] +/, ""))), c2.items.push({ type: "list_item", raw: l2, task: i2, checked: s2, loose: r2, text: t4 });
        }
        return c2;
      }
    }, t3.html = function(e3) {
      e3 = this.rules.block.html.exec(e3);
      if (e3)
        return { type: this.options.sanitize ? "paragraph" : "html", raw: e3[0], pre: !this.options.sanitizer && ("pre" === e3[1] || "script" === e3[1] || "style" === e3[1]), text: this.options.sanitize ? this.options.sanitizer ? this.options.sanitizer(e3[0]) : U(e3[0]) : e3[0] };
    }, t3.def = function(e3) {
      e3 = this.rules.block.def.exec(e3);
      if (e3)
        return e3[3] && (e3[3] = e3[3].substring(1, e3[3].length - 1)), { tag: e3[1].toLowerCase().replace(/\s+/g, " "), raw: e3[0], href: e3[2], title: e3[3] };
    }, t3.table = function(e3) {
      e3 = this.rules.block.table.exec(e3);
      if (e3) {
        var t4 = { type: "table", header: C(e3[1].replace(/^ *| *\| *$/g, "")), align: e3[2].replace(/^ *|\| *$/g, "").split(/ *\| */), cells: e3[3] ? e3[3].replace(/\n$/, "").split("\n") : [] };
        if (t4.header.length === t4.align.length) {
          t4.raw = e3[0];
          for (var n2 = t4.align.length, r2 = 0; r2 < n2; r2++)
            /^ *-+: *$/.test(t4.align[r2]) ? t4.align[r2] = "right" : /^ *:-+: *$/.test(t4.align[r2]) ? t4.align[r2] = "center" : /^ *:-+ *$/.test(t4.align[r2]) ? t4.align[r2] = "left" : t4.align[r2] = null;
          for (n2 = t4.cells.length, r2 = 0; r2 < n2; r2++)
            t4.cells[r2] = C(t4.cells[r2].replace(/^ *\| *| *\| *$/g, ""), t4.header.length);
          return t4;
        }
      }
    }, t3.lheading = function(e3) {
      e3 = this.rules.block.lheading.exec(e3);
      if (e3)
        return { type: "heading", raw: e3[0], depth: "=" === e3[2].charAt(0) ? 1 : 2, text: e3[1] };
    }, t3.paragraph = function(e3) {
      e3 = this.rules.block.paragraph.exec(e3);
      if (e3)
        return { type: "paragraph", raw: e3[0], text: "\n" === e3[1].charAt(e3[1].length - 1) ? e3[1].slice(0, -1) : e3[1] };
    }, t3.text = function(e3, t4) {
      e3 = this.rules.block.text.exec(e3);
      if (e3) {
        t4 = t4[t4.length - 1];
        return t4 && "text" === t4.type ? { raw: e3[0], text: e3[0] } : { type: "text", raw: e3[0], text: e3[0] };
      }
    }, t3.escape = function(e3) {
      e3 = this.rules.inline.escape.exec(e3);
      if (e3)
        return { type: "escape", raw: e3[0], text: U(e3[1]) };
    }, t3.tag = function(e3, t4, n2) {
      e3 = this.rules.inline.tag.exec(e3);
      if (e3)
        return !t4 && /^<a /i.test(e3[0]) ? t4 = true : t4 && /^<\/a>/i.test(e3[0]) && (t4 = false), !n2 && /^<(pre|code|kbd|script)(\s|>)/i.test(e3[0]) ? n2 = true : n2 && /^<\/(pre|code|kbd|script)(\s|>)/i.test(e3[0]) && (n2 = false), { type: this.options.sanitize ? "text" : "html", raw: e3[0], inLink: t4, inRawBlock: n2, text: this.options.sanitize ? this.options.sanitizer ? this.options.sanitizer(e3[0]) : U(e3[0]) : e3[0] };
    }, t3.link = function(e3) {
      var t4 = this.rules.inline.link.exec(e3);
      if (t4) {
        e3 = j(t4[2], "()");
        -1 < e3 && (r2 = (0 === t4[0].indexOf("!") ? 5 : 4) + t4[1].length + e3, t4[2] = t4[2].substring(0, e3), t4[0] = t4[0].substring(0, r2).trim(), t4[3] = "");
        var n2, e3 = t4[2], r2 = "";
        return r2 = this.options.pedantic ? (n2 = /^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(e3), n2 ? (e3 = n2[1], n2[3]) : "") : t4[3] ? t4[3].slice(1, -1) : "", E(t4, { href: (e3 = e3.trim().replace(/^<([\s\S]*)>$/, "$1")) && e3.replace(this.rules.inline._escapes, "$1"), title: r2 && r2.replace(this.rules.inline._escapes, "$1") }, t4[0]);
      }
    }, t3.reflink = function(e3, t4) {
      if ((n2 = this.rules.inline.reflink.exec(e3)) || (n2 = this.rules.inline.nolink.exec(e3))) {
        e3 = (n2[2] || n2[1]).replace(/\s+/g, " ");
        if ((e3 = t4[e3.toLowerCase()]) && e3.href)
          return E(n2, e3, n2[0]);
        var n2 = n2[0].charAt(0);
        return { type: "text", raw: n2, text: n2 };
      }
    }, t3.strong = function(e3, t4, n2) {
      void 0 === n2 && (n2 = "");
      var r2 = this.rules.inline.strong.start.exec(e3);
      if (r2 && (!r2[1] || r2[1] && ("" === n2 || this.rules.inline.punctuation.exec(n2)))) {
        t4 = t4.slice(-1 * e3.length);
        var i2, s2 = "**" === r2[0] ? this.rules.inline.strong.endAst : this.rules.inline.strong.endUnd;
        for (s2.lastIndex = 0; null != (r2 = s2.exec(t4)); )
          if (i2 = this.rules.inline.strong.middle.exec(t4.slice(0, r2.index + 3)))
            return { type: "strong", raw: e3.slice(0, i2[0].length), text: e3.slice(2, i2[0].length - 2) };
      }
    }, t3.em = function(e3, t4, n2) {
      void 0 === n2 && (n2 = "");
      var r2 = this.rules.inline.em.start.exec(e3);
      if (r2 && (!r2[1] || r2[1] && ("" === n2 || this.rules.inline.punctuation.exec(n2)))) {
        t4 = t4.slice(-1 * e3.length);
        var i2, s2 = "*" === r2[0] ? this.rules.inline.em.endAst : this.rules.inline.em.endUnd;
        for (s2.lastIndex = 0; null != (r2 = s2.exec(t4)); )
          if (i2 = this.rules.inline.em.middle.exec(t4.slice(0, r2.index + 2)))
            return { type: "em", raw: e3.slice(0, i2[0].length), text: e3.slice(1, i2[0].length - 1) };
      }
    }, t3.codespan = function(e3) {
      var t4 = this.rules.inline.code.exec(e3);
      if (t4) {
        var n2 = t4[2].replace(/\n/g, " "), r2 = /[^ ]/.test(n2), e3 = n2.startsWith(" ") && n2.endsWith(" ");
        return r2 && e3 && (n2 = n2.substring(1, n2.length - 1)), n2 = U(n2, true), { type: "codespan", raw: t4[0], text: n2 };
      }
    }, t3.br = function(e3) {
      e3 = this.rules.inline.br.exec(e3);
      if (e3)
        return { type: "br", raw: e3[0] };
    }, t3.del = function(e3) {
      e3 = this.rules.inline.del.exec(e3);
      if (e3)
        return { type: "del", raw: e3[0], text: e3[2] };
    }, t3.autolink = function(e3, t4) {
      e3 = this.rules.inline.autolink.exec(e3);
      if (e3) {
        var n2, t4 = "@" === e3[2] ? "mailto:" + (n2 = U(this.options.mangle ? t4(e3[1]) : e3[1])) : n2 = U(e3[1]);
        return { type: "link", raw: e3[0], text: n2, href: t4, tokens: [{ type: "text", raw: n2, text: n2 }] };
      }
    }, t3.url = function(e3, t4) {
      var n2, r2, i2, s2;
      if (n2 = this.rules.inline.url.exec(e3)) {
        if ("@" === n2[2])
          i2 = "mailto:" + (r2 = U(this.options.mangle ? t4(n2[0]) : n2[0]));
        else {
          for (; s2 = n2[0], n2[0] = this.rules.inline._backpedal.exec(n2[0])[0], s2 !== n2[0]; )
            ;
          r2 = U(n2[0]), i2 = "www." === n2[1] ? "http://" + r2 : r2;
        }
        return { type: "link", raw: n2[0], text: r2, href: i2, tokens: [{ type: "text", raw: r2, text: r2 }] };
      }
    }, t3.inlineText = function(e3, t4, n2) {
      e3 = this.rules.inline.text.exec(e3);
      if (e3) {
        n2 = t4 ? this.options.sanitize ? this.options.sanitizer ? this.options.sanitizer(e3[0]) : U(e3[0]) : e3[0] : U(this.options.smartypants ? n2(e3[0]) : e3[0]);
        return { type: "text", raw: e3[0], text: n2 };
      }
    }, e2;
  }(), R = $, T = z, $ = A, z = { newline: /^\n+/, code: /^( {4}[^\n]+\n*)+/, fences: /^ {0,3}(`{3,}(?=[^`\n]*\n)|~{3,})([^\n]*)\n(?:|([\s\S]*?)\n)(?: {0,3}\1[~`]* *(?:\n+|$)|$)/, hr: /^ {0,3}((?:- *){3,}|(?:_ *){3,}|(?:\* *){3,})(?:\n+|$)/, heading: /^ {0,3}(#{1,6}) +([^\n]*?)(?: +#+)? *(?:\n+|$)/, blockquote: /^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/, list: /^( {0,3})(bull) [\s\S]+?(?:hr|def|\n{2,}(?! )(?! {0,3}bull )\n*|\s*$)/, html: "^ {0,3}(?:<(script|pre|style)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:\\n{2,}|$)|<(?!script|pre|style)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:\\n{2,}|$)|</(?!script|pre|style)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:\\n{2,}|$))", def: /^ {0,3}\[(label)\]: *\n? *<?([^\s>]+)>?(?:(?: +\n? *| *\n *)(title))? *(?:\n+|$)/, nptable: R, table: R, lheading: /^([^\n]+)\n {0,3}(=+|-+) *(?:\n+|$)/, _paragraph: /^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html)[^\n]+)*)/, text: /^[^\n]+/, _label: /(?!\s*\])(?:\\[\[\]]|[^\[\]])+/, _title: /(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/ };
  z.def = T(z.def).replace("label", z._label).replace("title", z._title).getRegex(), z.bullet = /(?:[*+-]|\d{1,9}[.)])/, z.item = /^( *)(bull) ?[^\n]*(?:\n(?! *bull ?)[^\n]*)*/, z.item = T(z.item, "gm").replace(/bull/g, z.bullet).getRegex(), z.listItemStart = T(/^( *)(bull)/).replace("bull", z.bullet).getRegex(), z.list = T(z.list).replace(/bull/g, z.bullet).replace("hr", "\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def", "\\n+(?=" + z.def.source + ")").getRegex(), z._tag = "address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul", z._comment = /<!--(?!-?>)[\s\S]*?(?:-->|$)/, z.html = T(z.html, "i").replace("comment", z._comment).replace("tag", z._tag).replace("attribute", / +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(), z.paragraph = T(z._paragraph).replace("hr", z.hr).replace("heading", " {0,3}#{1,6} ").replace("|lheading", "").replace("blockquote", " {0,3}>").replace("fences", " {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list", " {0,3}(?:[*+-]|1[.)]) ").replace("html", "</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|!--)").replace("tag", z._tag).getRegex(), z.blockquote = T(z.blockquote).replace("paragraph", z.paragraph).getRegex(), z.normal = $({}, z), z.gfm = $({}, z.normal, { nptable: "^ *([^|\\n ].*\\|.*)\\n {0,3}([-:]+ *\\|[-| :]*)(?:\\n((?:(?!\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)", table: "^ *\\|(.+)\\n {0,3}\\|?( *[-:]+[-| :]*)(?:\\n *((?:(?!\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)" }), z.gfm.nptable = T(z.gfm.nptable).replace("hr", z.hr).replace("heading", " {0,3}#{1,6} ").replace("blockquote", " {0,3}>").replace("code", " {4}[^\\n]").replace("fences", " {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list", " {0,3}(?:[*+-]|1[.)]) ").replace("html", "</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|!--)").replace("tag", z._tag).getRegex(), z.gfm.table = T(z.gfm.table).replace("hr", z.hr).replace("heading", " {0,3}#{1,6} ").replace("blockquote", " {0,3}>").replace("code", " {4}[^\\n]").replace("fences", " {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list", " {0,3}(?:[*+-]|1[.)]) ").replace("html", "</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|!--)").replace("tag", z._tag).getRegex(), z.pedantic = $({}, z.normal, { html: T(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment", z._comment).replace(/tag/g, "(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(), def: /^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/, heading: /^ *(#{1,6}) *([^\n]+?) *(?:#+ *)?(?:\n+|$)/, fences: R, paragraph: T(z.normal._paragraph).replace("hr", z.hr).replace("heading", " *#{1,6} *[^\n]").replace("lheading", z.lheading).replace("blockquote", " {0,3}>").replace("|fences", "").replace("|list", "").replace("|html", "").getRegex() });
  R = { escape: /^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/, autolink: /^<(scheme:[^\s\x00-\x1f<>]*|email)>/, url: R, tag: "^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>", link: /^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/, reflink: /^!?\[(label)\]\[(?!\s*\])((?:\\[\[\]]?|[^\[\]\\])+)\]/, nolink: /^!?\[(?!\s*\])((?:\[[^\[\]]*\]|\\[\[\]]|[^\[\]])*)\](?:\[\])?/, reflinkSearch: "reflink|nolink(?!\\()", strong: { start: /^(?:(\*\*(?=[*punctuation]))|\*\*)(?![\s])|__/, middle: /^\*\*(?:(?:(?!overlapSkip)(?:[^*]|\\\*)|overlapSkip)|\*(?:(?!overlapSkip)(?:[^*]|\\\*)|overlapSkip)*?\*)+?\*\*$|^__(?![\s])((?:(?:(?!overlapSkip)(?:[^_]|\\_)|overlapSkip)|_(?:(?!overlapSkip)(?:[^_]|\\_)|overlapSkip)*?_)+?)__$/, endAst: /[^punctuation\s]\*\*(?!\*)|[punctuation]\*\*(?!\*)(?:(?=[punctuation_\s]|$))/, endUnd: /[^\s]__(?!_)(?:(?=[punctuation*\s])|$)/ }, em: { start: /^(?:(\*(?=[punctuation]))|\*)(?![*\s])|_/, middle: /^\*(?:(?:(?!overlapSkip)(?:[^*]|\\\*)|overlapSkip)|\*(?:(?!overlapSkip)(?:[^*]|\\\*)|overlapSkip)*?\*)+?\*$|^_(?![_\s])(?:(?:(?!overlapSkip)(?:[^_]|\\_)|overlapSkip)|_(?:(?!overlapSkip)(?:[^_]|\\_)|overlapSkip)*?_)+?_$/, endAst: /[^punctuation\s]\*(?!\*)|[punctuation]\*(?!\*)(?:(?=[punctuation_\s]|$))/, endUnd: /[^\s]_(?!_)(?:(?=[punctuation*\s])|$)/ }, code: /^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/, br: /^( {2,}|\\)\n(?!\s*$)/, del: R, text: /^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*]|\b_|$)|[^ ](?= {2,}\n)))/, punctuation: /^([\s*punctuation])/, _punctuation: "!\"#$%&'()+\\-.,/:;<=>?@\\[\\]`^{|}~" };
  R.punctuation = T(R.punctuation).replace(/punctuation/g, R._punctuation).getRegex(), R._blockSkip = "\\[[^\\]]*?\\]\\([^\\)]*?\\)|`[^`]*?`|<[^>]*?>", R._overlapSkip = "__[^_]*?__|\\*\\*\\[^\\*\\]*?\\*\\*", R._comment = T(z._comment).replace("(?:-->|$)", "-->").getRegex(), R.em.start = T(R.em.start).replace(/punctuation/g, R._punctuation).getRegex(), R.em.middle = T(R.em.middle).replace(/punctuation/g, R._punctuation).replace(/overlapSkip/g, R._overlapSkip).getRegex(), R.em.endAst = T(R.em.endAst, "g").replace(/punctuation/g, R._punctuation).getRegex(), R.em.endUnd = T(R.em.endUnd, "g").replace(/punctuation/g, R._punctuation).getRegex(), R.strong.start = T(R.strong.start).replace(/punctuation/g, R._punctuation).getRegex(), R.strong.middle = T(R.strong.middle).replace(/punctuation/g, R._punctuation).replace(/overlapSkip/g, R._overlapSkip).getRegex(), R.strong.endAst = T(R.strong.endAst, "g").replace(/punctuation/g, R._punctuation).getRegex(), R.strong.endUnd = T(R.strong.endUnd, "g").replace(/punctuation/g, R._punctuation).getRegex(), R.blockSkip = T(R._blockSkip, "g").getRegex(), R.overlapSkip = T(R._overlapSkip, "g").getRegex(), R._escapes = /\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g, R._scheme = /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/, R._email = /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/, R.autolink = T(R.autolink).replace("scheme", R._scheme).replace("email", R._email).getRegex(), R._attribute = /\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/, R.tag = T(R.tag).replace("comment", R._comment).replace("attribute", R._attribute).getRegex(), R._label = /(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/, R._href = /<(?:\\[<>]?|[^\s<>\\])*>|[^\s\x00-\x1f]*/, R._title = /"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/, R.link = T(R.link).replace("label", R._label).replace("href", R._href).replace("title", R._title).getRegex(), R.reflink = T(R.reflink).replace("label", R._label).getRegex(), R.reflinkSearch = T(R.reflinkSearch, "g").replace("reflink", R.reflink).replace("nolink", R.nolink).getRegex(), R.normal = $({}, R), R.pedantic = $({}, R.normal, { strong: { start: /^__|\*\*/, middle: /^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/, endAst: /\*\*(?!\*)/g, endUnd: /__(?!_)/g }, em: { start: /^_|\*/, middle: /^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/, endAst: /\*(?!\*)/g, endUnd: /_(?!_)/g }, link: T(/^!?\[(label)\]\((.*?)\)/).replace("label", R._label).getRegex(), reflink: T(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label", R._label).getRegex() }), R.gfm = $({}, R.normal, { escape: T(R.escape).replace("])", "~|])").getRegex(), _extended_email: /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/, url: /^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/, _backpedal: /(?:[^?!.,:;*_~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_~)]+(?!$))+/, del: /^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/, text: /^([`~]+|[^`~])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*~]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@))|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@))/ }), R.gfm.url = T(R.gfm.url, "i").replace("email", R.gfm._extended_email).getRegex(), R.breaks = $({}, R.gfm, { br: T(R.br).replace("{2,}", "*").getRegex(), text: T(R.gfm.text).replace("\\b_", "\\b_| {2,}\\n").replace(/\{2,\}/g, "*").getRegex() });
  var R = { block: z, inline: R }, P = t2.defaults, L = R.block, N = R.inline, B = Z;
  function F(e2) {
    return e2.replace(/---/g, "—").replace(/--/g, "–").replace(/(^|[-\u2014/(\[{"\s])'/g, "$1‘").replace(/'/g, "’").replace(/(^|[-\u2014/(\[{\u2018\s])"/g, "$1“").replace(/"/g, "”").replace(/\.{3}/g, "…");
  }
  function M(e2) {
    for (var t3, n2 = "", r2 = e2.length, i2 = 0; i2 < r2; i2++)
      t3 = e2.charCodeAt(i2), 0.5 < Math.random() && (t3 = "x" + t3.toString(16)), n2 += "&#" + t3 + ";";
    return n2;
  }
  var W = function() {
    function n2(e3) {
      this.tokens = [], this.tokens.links = /* @__PURE__ */ Object.create(null), this.options = e3 || P, this.options.tokenizer = this.options.tokenizer || new D(), this.tokenizer = this.options.tokenizer, this.tokenizer.options = this.options;
      e3 = { block: L.normal, inline: N.normal };
      this.options.pedantic ? (e3.block = L.pedantic, e3.inline = N.pedantic) : this.options.gfm && (e3.block = L.gfm, this.options.breaks ? e3.inline = N.breaks : e3.inline = N.gfm), this.tokenizer.rules = e3;
    }
    n2.lex = function(e3, t4) {
      return new n2(t4).lex(e3);
    }, n2.lexInline = function(e3, t4) {
      return new n2(t4).inlineTokens(e3);
    };
    var e2, t3, r2 = n2.prototype;
    return r2.lex = function(e3) {
      return e3 = e3.replace(/\r\n|\r/g, "\n").replace(/\t/g, "    "), this.blockTokens(e3, this.tokens, true), this.inline(this.tokens), this.tokens;
    }, r2.blockTokens = function(e3, t4, n3) {
      var r3, i2, s2, l2;
      for (void 0 === t4 && (t4 = []), void 0 === n3 && (n3 = true), e3 = e3.replace(/^ +$/gm, ""); e3; )
        if (r3 = this.tokenizer.space(e3))
          e3 = e3.substring(r3.raw.length), r3.type && t4.push(r3);
        else if (r3 = this.tokenizer.code(e3, t4))
          e3 = e3.substring(r3.raw.length), r3.type ? t4.push(r3) : ((l2 = t4[t4.length - 1]).raw += "\n" + r3.raw, l2.text += "\n" + r3.text);
        else if (r3 = this.tokenizer.fences(e3))
          e3 = e3.substring(r3.raw.length), t4.push(r3);
        else if (r3 = this.tokenizer.heading(e3))
          e3 = e3.substring(r3.raw.length), t4.push(r3);
        else if (r3 = this.tokenizer.nptable(e3))
          e3 = e3.substring(r3.raw.length), t4.push(r3);
        else if (r3 = this.tokenizer.hr(e3))
          e3 = e3.substring(r3.raw.length), t4.push(r3);
        else if (r3 = this.tokenizer.blockquote(e3))
          e3 = e3.substring(r3.raw.length), r3.tokens = this.blockTokens(r3.text, [], n3), t4.push(r3);
        else if (r3 = this.tokenizer.list(e3)) {
          for (e3 = e3.substring(r3.raw.length), s2 = r3.items.length, i2 = 0; i2 < s2; i2++)
            r3.items[i2].tokens = this.blockTokens(r3.items[i2].text, [], false);
          t4.push(r3);
        } else if (r3 = this.tokenizer.html(e3))
          e3 = e3.substring(r3.raw.length), t4.push(r3);
        else if (n3 && (r3 = this.tokenizer.def(e3)))
          e3 = e3.substring(r3.raw.length), this.tokens.links[r3.tag] || (this.tokens.links[r3.tag] = { href: r3.href, title: r3.title });
        else if (r3 = this.tokenizer.table(e3))
          e3 = e3.substring(r3.raw.length), t4.push(r3);
        else if (r3 = this.tokenizer.lheading(e3))
          e3 = e3.substring(r3.raw.length), t4.push(r3);
        else if (n3 && (r3 = this.tokenizer.paragraph(e3)))
          e3 = e3.substring(r3.raw.length), t4.push(r3);
        else if (r3 = this.tokenizer.text(e3, t4))
          e3 = e3.substring(r3.raw.length), r3.type ? t4.push(r3) : ((l2 = t4[t4.length - 1]).raw += "\n" + r3.raw, l2.text += "\n" + r3.text);
        else if (e3) {
          var a2 = "Infinite loop on byte: " + e3.charCodeAt(0);
          if (this.options.silent) {
            common_vendor.index.__f__("error", "at uni_modules/zero-markdown-view/components/mp-html/markdown/marked.min.js:6", a2);
            break;
          }
          throw new Error(a2);
        }
      return t4;
    }, r2.inline = function(e3) {
      for (var t4, n3, r3, i2, s2, l2 = e3.length, a2 = 0; a2 < l2; a2++)
        switch ((s2 = e3[a2]).type) {
          case "paragraph":
          case "text":
          case "heading":
            s2.tokens = [], this.inlineTokens(s2.text, s2.tokens);
            break;
          case "table":
            for (s2.tokens = { header: [], cells: [] }, r3 = s2.header.length, t4 = 0; t4 < r3; t4++)
              s2.tokens.header[t4] = [], this.inlineTokens(s2.header[t4], s2.tokens.header[t4]);
            for (r3 = s2.cells.length, t4 = 0; t4 < r3; t4++)
              for (i2 = s2.cells[t4], s2.tokens.cells[t4] = [], n3 = 0; n3 < i2.length; n3++)
                s2.tokens.cells[t4][n3] = [], this.inlineTokens(i2[n3], s2.tokens.cells[t4][n3]);
            break;
          case "blockquote":
            this.inline(s2.tokens);
            break;
          case "list":
            for (r3 = s2.items.length, t4 = 0; t4 < r3; t4++)
              this.inline(s2.items[t4].tokens);
        }
      return e3;
    }, r2.inlineTokens = function(e3, t4, n3, r3) {
      var i2;
      void 0 === t4 && (t4 = []), void 0 === n3 && (n3 = false), void 0 === r3 && (r3 = false);
      var s2, l2, a2, o2 = e3;
      if (this.tokens.links) {
        var c2 = Object.keys(this.tokens.links);
        if (0 < c2.length)
          for (; null != (s2 = this.tokenizer.rules.inline.reflinkSearch.exec(o2)); )
            c2.includes(s2[0].slice(s2[0].lastIndexOf("[") + 1, -1)) && (o2 = o2.slice(0, s2.index) + "[" + B("a", s2[0].length - 2) + "]" + o2.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex));
      }
      for (; null != (s2 = this.tokenizer.rules.inline.blockSkip.exec(o2)); )
        o2 = o2.slice(0, s2.index) + "[" + B("a", s2[0].length - 2) + "]" + o2.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);
      for (; e3; )
        if (l2 || (a2 = ""), l2 = false, i2 = this.tokenizer.escape(e3))
          e3 = e3.substring(i2.raw.length), t4.push(i2);
        else if (i2 = this.tokenizer.tag(e3, n3, r3))
          e3 = e3.substring(i2.raw.length), n3 = i2.inLink, r3 = i2.inRawBlock, t4.push(i2);
        else if (i2 = this.tokenizer.link(e3))
          e3 = e3.substring(i2.raw.length), "link" === i2.type && (i2.tokens = this.inlineTokens(i2.text, [], true, r3)), t4.push(i2);
        else if (i2 = this.tokenizer.reflink(e3, this.tokens.links))
          e3 = e3.substring(i2.raw.length), "link" === i2.type && (i2.tokens = this.inlineTokens(i2.text, [], true, r3)), t4.push(i2);
        else if (i2 = this.tokenizer.strong(e3, o2, a2))
          e3 = e3.substring(i2.raw.length), i2.tokens = this.inlineTokens(i2.text, [], n3, r3), t4.push(i2);
        else if (i2 = this.tokenizer.em(e3, o2, a2))
          e3 = e3.substring(i2.raw.length), i2.tokens = this.inlineTokens(i2.text, [], n3, r3), t4.push(i2);
        else if (i2 = this.tokenizer.codespan(e3))
          e3 = e3.substring(i2.raw.length), t4.push(i2);
        else if (i2 = this.tokenizer.br(e3))
          e3 = e3.substring(i2.raw.length), t4.push(i2);
        else if (i2 = this.tokenizer.del(e3))
          e3 = e3.substring(i2.raw.length), i2.tokens = this.inlineTokens(i2.text, [], n3, r3), t4.push(i2);
        else if (i2 = this.tokenizer.autolink(e3, M))
          e3 = e3.substring(i2.raw.length), t4.push(i2);
        else if (n3 || !(i2 = this.tokenizer.url(e3, M))) {
          if (i2 = this.tokenizer.inlineText(e3, r3, F))
            e3 = e3.substring(i2.raw.length), a2 = i2.raw.slice(-1), l2 = true, t4.push(i2);
          else if (e3) {
            var u2 = "Infinite loop on byte: " + e3.charCodeAt(0);
            if (this.options.silent) {
              common_vendor.index.__f__("error", "at uni_modules/zero-markdown-view/components/mp-html/markdown/marked.min.js:6", u2);
              break;
            }
            throw new Error(u2);
          }
        } else
          e3 = e3.substring(i2.raw.length), t4.push(i2);
      return t4;
    }, e2 = n2, t3 = [{ key: "rules", get: function() {
      return { block: L, inline: N };
    } }], (r2 = null) && i(e2.prototype, r2), t3 && i(e2, t3), n2;
  }(), X = t2.defaults, G = S, V = _, H = function() {
    function e2(e3) {
      this.options = e3 || X;
    }
    var t3 = e2.prototype;
    return t3.code = function(e3, t4, n2) {
      var r2 = (t4 || "").match(/\S*/)[0];
      return !this.options.highlight || null != (t4 = this.options.highlight(e3, r2)) && t4 !== e3 && (n2 = true, e3 = t4), r2 ? '<pre><code class="' + this.options.langPrefix + V(r2, true) + '">' + (n2 ? e3 : V(e3, true)) + "</code></pre>\n" : "<pre><code>" + (n2 ? e3 : V(e3, true)) + "</code></pre>\n";
    }, t3.blockquote = function(e3) {
      return "<blockquote>\n" + e3 + "</blockquote>\n";
    }, t3.html = function(e3) {
      return e3;
    }, t3.heading = function(e3, t4, n2, r2) {
      return this.options.headerIds ? "<h" + t4 + ' id="' + this.options.headerPrefix + r2.slug(n2) + '">' + e3 + "</h" + t4 + ">\n" : "<h" + t4 + ">" + e3 + "</h" + t4 + ">\n";
    }, t3.hr = function() {
      return this.options.xhtml ? "<hr/>\n" : "<hr>\n";
    }, t3.list = function(e3, t4, n2) {
      var r2 = t4 ? "ol" : "ul";
      return "<" + r2 + (t4 && 1 !== n2 ? ' start="' + n2 + '"' : "") + ">\n" + e3 + "</" + r2 + ">\n";
    }, t3.listitem = function(e3) {
      return "<li>" + e3 + "</li>\n";
    }, t3.checkbox = function(e3) {
      return "<input " + (e3 ? 'checked="" ' : "") + 'disabled="" type="checkbox"' + (this.options.xhtml ? " /" : "") + "> ";
    }, t3.paragraph = function(e3) {
      return "<p>" + e3 + "</p>\n";
    }, t3.table = function(e3, t4) {
      return "<table>\n<thead>\n" + e3 + "</thead>\n" + (t4 = t4 && "<tbody>" + t4 + "</tbody>") + "</table>\n";
    }, t3.tablerow = function(e3) {
      return "<tr>\n" + e3 + "</tr>\n";
    }, t3.tablecell = function(e3, t4) {
      var n2 = t4.header ? "th" : "td";
      return (t4.align ? "<" + n2 + ' align="' + t4.align + '">' : "<" + n2 + ">") + e3 + "</" + n2 + ">\n";
    }, t3.strong = function(e3) {
      return "<strong>" + e3 + "</strong>";
    }, t3.em = function(e3) {
      return "<em>" + e3 + "</em>";
    }, t3.codespan = function(e3) {
      return "<code>" + e3 + "</code>";
    }, t3.br = function() {
      return this.options.xhtml ? "<br/>" : "<br>";
    }, t3.del = function(e3) {
      return "<del>" + e3 + "</del>";
    }, t3.link = function(e3, t4, n2) {
      if (null === (e3 = G(this.options.sanitize, this.options.baseUrl, e3)))
        return n2;
      e3 = '<a href="' + V(e3) + '"';
      return t4 && (e3 += ' title="' + t4 + '"'), e3 += ">" + n2 + "</a>";
    }, t3.image = function(e3, t4, n2) {
      if (null === (e3 = G(this.options.sanitize, this.options.baseUrl, e3)))
        return n2;
      n2 = '<img src="' + e3 + '" alt="' + n2 + '"';
      return t4 && (n2 += ' title="' + t4 + '"'), n2 += this.options.xhtml ? "/>" : ">";
    }, t3.text = function(e3) {
      return e3;
    }, e2;
  }(), J = function() {
    function e2() {
    }
    var t3 = e2.prototype;
    return t3.strong = function(e3) {
      return e3;
    }, t3.em = function(e3) {
      return e3;
    }, t3.codespan = function(e3) {
      return e3;
    }, t3.del = function(e3) {
      return e3;
    }, t3.html = function(e3) {
      return e3;
    }, t3.text = function(e3) {
      return e3;
    }, t3.link = function(e3, t4, n2) {
      return "" + n2;
    }, t3.image = function(e3, t4, n2) {
      return "" + n2;
    }, t3.br = function() {
      return "";
    }, e2;
  }(), K = function() {
    function e2() {
      this.seen = {};
    }
    var t3 = e2.prototype;
    return t3.serialize = function(e3) {
      return e3.toLowerCase().trim().replace(/<[!\/a-z].*?>/gi, "").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g, "").replace(/\s/g, "-");
    }, t3.getNextSafeSlug = function(e3, t4) {
      var n2 = e3, r2 = 0;
      if (this.seen.hasOwnProperty(n2))
        for (r2 = this.seen[e3]; n2 = e3 + "-" + ++r2, this.seen.hasOwnProperty(n2); )
          ;
      return t4 || (this.seen[e3] = r2, this.seen[n2] = 0), n2;
    }, t3.slug = function(e3, t4) {
      void 0 === t4 && (t4 = {});
      var n2 = this.serialize(e3);
      return this.getNextSafeSlug(n2, t4.dryrun);
    }, e2;
  }(), Q = t2.defaults, Y = y, ee = function() {
    function n2(e3) {
      this.options = e3 || Q, this.options.renderer = this.options.renderer || new H(), this.renderer = this.options.renderer, this.renderer.options = this.options, this.textRenderer = new J(), this.slugger = new K();
    }
    n2.parse = function(e3, t3) {
      return new n2(t3).parse(e3);
    }, n2.parseInline = function(e3, t3) {
      return new n2(t3).parseInline(e3);
    };
    var e2 = n2.prototype;
    return e2.parse = function(e3, t3) {
      void 0 === t3 && (t3 = true);
      for (var n3, r2, i2, s2, l2, a2, o2, c2, u2, p2, h2, g2, f2, d2, k2, b2 = "", m2 = e3.length, x2 = 0; x2 < m2; x2++)
        switch ((c2 = e3[x2]).type) {
          case "space":
            continue;
          case "hr":
            b2 += this.renderer.hr();
            continue;
          case "heading":
            b2 += this.renderer.heading(this.parseInline(c2.tokens), c2.depth, Y(this.parseInline(c2.tokens, this.textRenderer)), this.slugger);
            continue;
          case "code":
            b2 += this.renderer.code(c2.text, c2.lang, c2.escaped);
            continue;
          case "table":
            for (a2 = u2 = "", i2 = c2.header.length, n3 = 0; n3 < i2; n3++)
              a2 += this.renderer.tablecell(this.parseInline(c2.tokens.header[n3]), { header: true, align: c2.align[n3] });
            for (u2 += this.renderer.tablerow(a2), o2 = "", i2 = c2.cells.length, n3 = 0; n3 < i2; n3++) {
              for (a2 = "", s2 = (l2 = c2.tokens.cells[n3]).length, r2 = 0; r2 < s2; r2++)
                a2 += this.renderer.tablecell(this.parseInline(l2[r2]), { header: false, align: c2.align[r2] });
              o2 += this.renderer.tablerow(a2);
            }
            b2 += this.renderer.table(u2, o2);
            continue;
          case "blockquote":
            o2 = this.parse(c2.tokens), b2 += this.renderer.blockquote(o2);
            continue;
          case "list":
            for (u2 = c2.ordered, w2 = c2.start, p2 = c2.loose, i2 = c2.items.length, o2 = "", n3 = 0; n3 < i2; n3++)
              f2 = (g2 = c2.items[n3]).checked, d2 = g2.task, h2 = "", g2.task && (k2 = this.renderer.checkbox(f2), p2 ? 0 < g2.tokens.length && "text" === g2.tokens[0].type ? (g2.tokens[0].text = k2 + " " + g2.tokens[0].text, g2.tokens[0].tokens && 0 < g2.tokens[0].tokens.length && "text" === g2.tokens[0].tokens[0].type && (g2.tokens[0].tokens[0].text = k2 + " " + g2.tokens[0].tokens[0].text)) : g2.tokens.unshift({ type: "text", text: k2 }) : h2 += k2), h2 += this.parse(g2.tokens, p2), o2 += this.renderer.listitem(h2, d2, f2);
            b2 += this.renderer.list(o2, u2, w2);
            continue;
          case "html":
            b2 += this.renderer.html(c2.text);
            continue;
          case "paragraph":
            b2 += this.renderer.paragraph(this.parseInline(c2.tokens));
            continue;
          case "text":
            for (o2 = c2.tokens ? this.parseInline(c2.tokens) : c2.text; x2 + 1 < m2 && "text" === e3[x2 + 1].type; )
              o2 += "\n" + ((c2 = e3[++x2]).tokens ? this.parseInline(c2.tokens) : c2.text);
            b2 += t3 ? this.renderer.paragraph(o2) : o2;
            continue;
          default:
            var w2 = 'Token with "' + c2.type + '" type was not found.';
            if (this.options.silent)
              return void common_vendor.index.__f__("error", "at uni_modules/zero-markdown-view/components/mp-html/markdown/marked.min.js:6", w2);
            throw new Error(w2);
        }
      return b2;
    }, e2.parseInline = function(e3, t3) {
      t3 = t3 || this.renderer;
      for (var n3, r2 = "", i2 = e3.length, s2 = 0; s2 < i2; s2++)
        switch ((n3 = e3[s2]).type) {
          case "escape":
            r2 += t3.text(n3.text);
            break;
          case "html":
            r2 += t3.html(n3.text);
            break;
          case "link":
            r2 += t3.link(n3.href, n3.title, this.parseInline(n3.tokens, t3));
            break;
          case "image":
            r2 += t3.image(n3.href, n3.title, n3.text);
            break;
          case "strong":
            r2 += t3.strong(this.parseInline(n3.tokens, t3));
            break;
          case "em":
            r2 += t3.em(this.parseInline(n3.tokens, t3));
            break;
          case "codespan":
            r2 += t3.codespan(n3.text);
            break;
          case "br":
            r2 += t3.br();
            break;
          case "del":
            r2 += t3.del(this.parseInline(n3.tokens, t3));
            break;
          case "text":
            r2 += t3.text(n3.text);
            break;
          default:
            var l2 = 'Token with "' + n3.type + '" type was not found.';
            if (this.options.silent)
              return void common_vendor.index.__f__("error", "at uni_modules/zero-markdown-view/components/mp-html/markdown/marked.min.js:6", l2);
            throw new Error(l2);
        }
      return r2;
    }, n2;
  }(), te = A, ne = I, re = _, _ = t2.getDefaults, ie = t2.changeDefaults, t2 = t2.defaults;
  function se(e2, n2, r2) {
    if (null == e2)
      throw new Error("marked(): input parameter is undefined or null");
    if ("string" != typeof e2)
      throw new Error("marked(): input parameter is of type " + Object.prototype.toString.call(e2) + ", string expected");
    if ("function" == typeof n2 && (r2 = n2, n2 = null), n2 = te({}, se.defaults, n2 || {}), ne(n2), r2) {
      var i2, s2 = n2.highlight;
      try {
        i2 = W.lex(e2, n2);
      } catch (e3) {
        return r2(e3);
      }
      var l2 = function(t4) {
        var e3;
        if (!t4)
          try {
            e3 = ee.parse(i2, n2);
          } catch (e4) {
            t4 = e4;
          }
        return n2.highlight = s2, t4 ? r2(t4) : r2(null, e3);
      };
      if (!s2 || s2.length < 3)
        return l2();
      if (delete n2.highlight, !i2.length)
        return l2();
      var a2 = 0;
      return se.walkTokens(i2, function(n3) {
        "code" === n3.type && (a2++, setTimeout(function() {
          s2(n3.text, n3.lang, function(e3, t4) {
            return e3 ? l2(e3) : (null != t4 && t4 !== n3.text && (n3.text = t4, n3.escaped = true), void (0 === --a2 && l2()));
          });
        }, 0));
      }), void (0 === a2 && l2());
    }
    try {
      var t3 = W.lex(e2, n2);
      return n2.walkTokens && se.walkTokens(t3, n2.walkTokens), ee.parse(t3, n2);
    } catch (e3) {
      if (e3.message += "\nPlease report this to https://github.com/markedjs/marked.", n2.silent)
        return "<p>An error occurred:</p><pre>" + re(e3.message + "", true) + "</pre>";
      throw e3;
    }
  }
  return se.options = se.setOptions = function(e2) {
    return te(se.defaults, e2), ie(se.defaults), se;
  }, se.getDefaults = _, se.defaults = t2, se.use = function(a2) {
    var t3, n2 = te({}, a2);
    a2.renderer && function() {
      var e2, l2 = se.defaults.renderer || new H();
      for (e2 in a2.renderer)
        !function(i2) {
          var s2 = l2[i2];
          l2[i2] = function() {
            for (var e3 = arguments.length, t4 = new Array(e3), n3 = 0; n3 < e3; n3++)
              t4[n3] = arguments[n3];
            var r2 = a2.renderer[i2].apply(l2, t4);
            return false === r2 && (r2 = s2.apply(l2, t4)), r2;
          };
        }(e2);
      n2.renderer = l2;
    }(), a2.tokenizer && function() {
      var e2, l2 = se.defaults.tokenizer || new D();
      for (e2 in a2.tokenizer)
        !function(i2) {
          var s2 = l2[i2];
          l2[i2] = function() {
            for (var e3 = arguments.length, t4 = new Array(e3), n3 = 0; n3 < e3; n3++)
              t4[n3] = arguments[n3];
            var r2 = a2.tokenizer[i2].apply(l2, t4);
            return false === r2 && (r2 = s2.apply(l2, t4)), r2;
          };
        }(e2);
      n2.tokenizer = l2;
    }(), a2.walkTokens && (t3 = se.defaults.walkTokens, n2.walkTokens = function(e2) {
      a2.walkTokens(e2), t3 && t3(e2);
    }), se.setOptions(n2);
  }, se.walkTokens = function(e2, t3) {
    for (var n2, r2 = p(e2); !(n2 = r2()).done; ) {
      var i2 = n2.value;
      switch (t3(i2), i2.type) {
        case "table":
          for (var s2 = p(i2.tokens.header); !(l2 = s2()).done; ) {
            var l2 = l2.value;
            se.walkTokens(l2, t3);
          }
          for (var a2, o2 = p(i2.tokens.cells); !(a2 = o2()).done; )
            for (var c2 = p(a2.value); !(u2 = c2()).done; ) {
              var u2 = u2.value;
              se.walkTokens(u2, t3);
            }
          break;
        case "list":
          se.walkTokens(i2.items, t3);
          break;
        default:
          i2.tokens && se.walkTokens(i2.tokens, t3);
      }
    }
  }, se.parseInline = function(e2, t3) {
    if (null == e2)
      throw new Error("marked.parseInline(): input parameter is undefined or null");
    if ("string" != typeof e2)
      throw new Error("marked.parseInline(): input parameter is of type " + Object.prototype.toString.call(e2) + ", string expected");
    t3 = te({}, se.defaults, t3 || {}), ne(t3);
    try {
      var n2 = W.lexInline(e2, t3);
      return t3.walkTokens && se.walkTokens(n2, t3.walkTokens), ee.parseInline(n2, t3);
    } catch (e3) {
      if (e3.message += "\nPlease report this to https://github.com/markedjs/marked.", t3.silent)
        return "<p>An error occurred:</p><pre>" + re(e3.message + "", true) + "</pre>";
      throw e3;
    }
  }, se.Parser = ee, se.parser = ee.parse, se.Renderer = H, se.TextRenderer = J, se.Lexer = W, se.lexer = W.lex, se.Tokenizer = D, se.Slugger = K, se.parse = se;
}
const marked = t();
exports.marked = marked;
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/uni_modules/zero-markdown-view/components/mp-html/markdown/marked.min.js.map
