<view class="uni-cursor-point"><view wx:if="{{a}}" class="{{[p && 'uni-fab--leftBottom', q && 'uni-fab--rightBottom', r && 'uni-fab--leftTop', s && 'uni-fab--rightTop', 'uni-fab']}}"><view class="{{[h && 'uni-fab__content--left', i && 'uni-fab__content--right', j && 'uni-fab__content--flexDirection', k && 'uni-fab__content--flexDirectionStart', l && 'uni-fab__content--flexDirectionEnd', m && 'uni-fab__content--other-platform', 'uni-fab__content']}}" style="{{'width:' + n + ';' + ('height:' + o)}}" elevation="5"><view wx:if="{{b}}" class="uni-fab__item uni-fab__item--first"/><view wx:for="{{c}}" wx:for-item="item" wx:key="d" class="{{[e && 'uni-fab__item--active', f && 'horizontal-margin', 'uni-fab__item']}}" bindtap="{{item.e}}"><image src="{{item.a}}" class="uni-fab__item-image" mode="aspectFit"/><text class="{{['uni-fab__item-text', d && 'vertical-margin']}}" style="{{'color:' + item.c}}">{{item.b}}</text></view><view wx:if="{{g}}" class="uni-fab__item uni-fab__item--first"/></view></view><view class="{{[w && 'uni-fab__circle--leftBottom', x && 'uni-fab__circle--rightBottom', y && 'uni-fab__circle--leftTop', z && 'uni-fab__circle--rightTop', A && 'uni-fab__content--other-platform', 'uni-fab__circle', 'uni-fab__plus']}}" style="{{'background-color:' + 'var(--ui-BG-Main)'}}" bindtap="{{B}}"><uni-icons wx:if="{{v}}" class="{{['fab-circle-icon', t && 'uni-fab__plus--active']}}" u-i="547e6c72-0" bind:__l="__l" u-p="{{v}}"></uni-icons></view></view>