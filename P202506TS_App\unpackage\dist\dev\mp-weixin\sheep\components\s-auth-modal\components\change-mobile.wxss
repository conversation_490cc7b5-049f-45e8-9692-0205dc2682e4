/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
@keyframes title-animation-146b49fd {
0% {
    font-size: 32rpx;
}
100% {
    font-size: 36rpx;
}
}
.login-wrap.data-v-146b49fd {
  padding: 50rpx 34rpx;
  min-height: 500rpx;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
}
.head-box .head-title.data-v-146b49fd {
  min-width: 160rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  line-height: 36rpx;
}
.head-box .head-title-active.data-v-146b49fd {
  width: 160rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #999;
  line-height: 36rpx;
}
.head-box .head-title-animation.data-v-146b49fd {
  animation-name: title-animation-146b49fd;
  animation-duration: 0.1s;
  animation-timing-function: ease-out;
  animation-fill-mode: forwards;
}
.head-box .head-title-line.data-v-146b49fd {
  position: relative;
}
.head-box .head-title-line.data-v-146b49fd::before {
  content: "";
  width: 1rpx;
  height: 34rpx;
  background-color: #e4e7ed;
  position: absolute;
  left: -30rpx;
  top: 50%;
  transform: translateY(-50%);
}
.head-box .head-subtitle.data-v-146b49fd {
  font-size: 26rpx;
  font-weight: 400;
  color: #afb6c0;
  text-align: left;
  display: flex;
}
.code-btn-start.data-v-146b49fd {
  width: 160rpx;
  height: 56rpx;
  line-height: normal;
  border: 2rpx solid var(--ui-BG-Main);
  border-radius: 28rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: var(--ui-BG-Main);
  opacity: 1;
}
.forgot-btn.data-v-146b49fd {
  width: 160rpx;
  line-height: 56rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #999;
}
.login-btn-start.data-v-146b49fd {
  width: 158rpx;
  height: 56rpx;
  line-height: normal;
  background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
  border-radius: 28rpx;
  font-size: 26rpx;
  font-weight: 500;
  color: #fff;
}
.type-btn.data-v-146b49fd {
  padding: 20rpx;
  margin: 40rpx auto;
  width: 200rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #999999;
}
.auto-login-box.data-v-146b49fd {
  width: 100%;
}
.auto-login-box .auto-login-btn.data-v-146b49fd {
  width: 68rpx;
  height: 68rpx;
  border-radius: 50%;
  margin: 0 30rpx;
}
.auto-login-box .auto-login-img.data-v-146b49fd {
  width: 68rpx;
  height: 68rpx;
  border-radius: 50%;
}
.agreement-box.data-v-146b49fd {
  margin: 80rpx auto 0;
}
.agreement-box .protocol-check.data-v-146b49fd {
  transform: scale(0.7);
}
.agreement-box .agreement-text.data-v-146b49fd {
  font-size: 26rpx;
  font-weight: 500;
  color: #999999;
}
.agreement-box .agreement-text .tcp-text.data-v-146b49fd {
  color: var(--ui-BG-Main);
}
.editPwd-btn-box .save-btn.data-v-146b49fd {
  width: 690rpx;
  line-height: 70rpx;
  background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
  border-radius: 35rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #ffffff;
}
.editPwd-btn-box .forgot-btn.data-v-146b49fd {
  width: 690rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #999999;
}