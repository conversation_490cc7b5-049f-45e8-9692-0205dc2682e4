<view class="data-v-859b5179"><view class="head-box ss-m-b-60 data-v-859b5179"><view class="head-title ss-m-b-20 data-v-859b5179">重置密码</view><view class="head-subtitle data-v-859b5179">为了您的账号安全，设置密码前请先进行安全验证</view></view><uni-forms wx:if="{{q}}" class="r data-v-859b5179" u-s="{{['d']}}" u-r="resetPasswordRef" u-i="859b5179-0" bind:__l="__l" bindupdateModelValue="{{p}}" u-p="{{q}}"><uni-forms-item wx:if="{{g}}" class="data-v-859b5179" u-s="{{['d']}}" u-i="859b5179-1,859b5179-0" bind:__l="__l" u-p="{{g}}"><uni-easyinput wx:if="{{f}}" class="data-v-859b5179" u-s="{{['right']}}" u-i="859b5179-2,859b5179-1" bind:__l="__l" bindupdateModelValue="{{e}}" u-p="{{f}}"><button disabled="{{b}}" class="{{['ss-reset-button', 'code-btn', 'code-btn-start', 'data-v-859b5179', c && 'code-btn-end']}}" bindtap="{{d}}" slot="right">{{a}}</button></uni-easyinput></uni-forms-item><uni-forms-item wx:if="{{j}}" class="data-v-859b5179" u-s="{{['d']}}" u-i="859b5179-3,859b5179-0" bind:__l="__l" u-p="{{j}}"><uni-easyinput wx:if="{{i}}" class="data-v-859b5179" u-i="859b5179-4,859b5179-3" bind:__l="__l" bindupdateModelValue="{{h}}" u-p="{{i}}"/></uni-forms-item><uni-forms-item wx:if="{{n}}" class="data-v-859b5179" u-s="{{['d']}}" u-i="859b5179-5,859b5179-0" bind:__l="__l" u-p="{{n}}"><uni-easyinput wx:if="{{m}}" class="data-v-859b5179" u-s="{{['right']}}" u-i="859b5179-6,859b5179-5" bind:__l="__l" bindupdateModelValue="{{l}}" u-p="{{m}}"><button class="ss-reset-button login-btn-start data-v-859b5179" bindtap="{{k}}" slot="right"> 确认 </button></uni-easyinput></uni-forms-item></uni-forms><button wx:if="{{r}}" class="ss-reset-button type-btn data-v-859b5179" bindtap="{{s}}"> 返回登录 </button></view>