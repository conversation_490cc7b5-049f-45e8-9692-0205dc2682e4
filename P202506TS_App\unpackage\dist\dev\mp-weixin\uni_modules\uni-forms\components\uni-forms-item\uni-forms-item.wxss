/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-forms-item {
  position: relative;
  padding: 0px;
  text-align: left;
  color: #333;
  font-size: 14px;
}
.uni-forms-item__box {
  position: relative;
}
.uni-forms-item__inner {
  display: flex;
  padding-bottom: 22px;
}
.is-direction-left {
  flex-direction: row;
}
.is-direction-top {
  flex-direction: column;
}
.uni-forms-item__label {
  display: flex;
  flex-shrink: 0;
  box-sizing: border-box;
  flex-direction: row;
  align-items: center;
  width: 65px;
  padding: 5px 0;
  height: 72rpx;
}
.uni-forms-item__label .label-text {
  font-size: 28rpx;
  color: #333333;
}
.uni-forms-item__label .label-seat {
  margin-right: 5px;
}
.uni-forms-item__content {
  width: 100%;
  box-sizing: border-box;
  min-height: 36px;
  flex: 1;
}
.label-icon {
  margin-right: 5px;
  margin-top: -1px;
}
.is-required {
  color: #dd524d;
  font-weight: bold;
}
.uni-error-message {
  position: absolute;
  bottom: 0px;
  left: 0;
  text-align: left;
}
.uni-error-message-text {
  line-height: 44rpx;
  color: #dd524d;
  font-size: 24rpx;
}
.uni-error-msg--boeder {
  position: relative;
  bottom: 0;
  line-height: 22px;
}
.is-input-error-border {
  border-color: #dd524d;
}
.uni-forms-item--border {
  margin-bottom: 0;
  padding: 10px 0;
  border-top: 1px #eee solid;
}
.uni-forms-item--border .uni-forms-item__inner {
  padding: 0;
}
.is-first-border {
  border: none;
}
.uni-forms--no-padding {
  padding: 0;
}