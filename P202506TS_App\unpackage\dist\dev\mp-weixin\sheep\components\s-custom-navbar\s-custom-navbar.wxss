/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.nav-box.data-v-6417c6b5 {
  width: 750rpx;
  position: relative;
  height: 100%;
}
.nav-box .nav-item.data-v-6417c6b5 {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}
.nav-box .nav-icon.data-v-6417c6b5 {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 20rpx;
}
.nav-box .nav-icon .inner-icon-box.data-v-6417c6b5 {
  border: 1px solid rgba(255, 255, 255, 0.4);
  background: none !important;
}
.nav-box .nav-icon .icon-box.data-v-6417c6b5 {
  background: #ffffff;
  box-shadow: 0px 0px 4rpx rgba(51, 51, 51, 0.08), 0px 4rpx 6rpx 2rpx rgba(102, 102, 102, 0.12);
  border-radius: 30rpx;
  width: 134rpx;
  height: 56rpx;
  margin-left: 8rpx;
}
.nav-box .nav-icon .icon-box .line.data-v-6417c6b5 {
  width: 2rpx;
  height: 24rpx;
  background: #e5e5e7;
}
.nav-box .nav-icon .icon-box .sicon-back.data-v-6417c6b5 {
  font-size: 32rpx;
}
.nav-box .nav-icon .icon-box .sicon-home.data-v-6417c6b5 {
  font-size: 32rpx;
}
.nav-box .nav-icon .icon-box .sicon-more.data-v-6417c6b5 {
  font-size: 32rpx;
}
.nav-box .nav-icon .icon-box .icon-button.data-v-6417c6b5 {
  width: 67rpx;
  height: 56rpx;
}
.nav-box .nav-icon .icon-box .icon-button-left.data-v-6417c6b5:hover {
  background: rgba(0, 0, 0, 0.16);
  border-radius: 30rpx 0px 0px 30rpx;
}
.nav-box .nav-icon .icon-box .icon-button-right.data-v-6417c6b5:hover {
  background: rgba(0, 0, 0, 0.16);
  border-radius: 0px 30rpx 30rpx 0px;
}