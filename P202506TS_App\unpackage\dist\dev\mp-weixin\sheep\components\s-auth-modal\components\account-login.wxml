<view class="data-v-f21c903c"><view class="head-box ss-m-b-60 ss-flex-col data-v-f21c903c"><view class="ss-flex ss-m-b-20 data-v-f21c903c"><view class="head-title-active head-title-line data-v-f21c903c" bindtap="{{a}}"> 短信登录 </view><view class="head-title ss-m-r-40 head-title-animation data-v-f21c903c">账号登录</view></view><view class="head-subtitle data-v-f21c903c">如果未设置过密码，请点击忘记密码</view></view><uni-forms wx:if="{{l}}" class="r data-v-f21c903c" u-s="{{['d']}}" u-r="accountLoginRef" u-i="f21c903c-0" bind:__l="__l" bindupdateModelValue="{{k}}" u-p="{{l}}"><uni-forms-item wx:if="{{e}}" class="data-v-f21c903c" u-s="{{['d']}}" u-i="f21c903c-1,f21c903c-0" bind:__l="__l" u-p="{{e}}"><uni-easyinput wx:if="{{d}}" class="data-v-f21c903c" u-s="{{['right']}}" u-i="f21c903c-2,f21c903c-1" bind:__l="__l" bindupdateModelValue="{{c}}" u-p="{{d}}"><button class="ss-reset-button forgot-btn data-v-f21c903c" bindtap="{{b}}" slot="right"> 忘记密码 </button></uni-easyinput></uni-forms-item><uni-forms-item wx:if="{{i}}" class="data-v-f21c903c" u-s="{{['d']}}" u-i="f21c903c-3,f21c903c-0" bind:__l="__l" u-p="{{i}}"><uni-easyinput wx:if="{{h}}" class="data-v-f21c903c" u-s="{{['right']}}" u-i="f21c903c-4,f21c903c-3" bind:__l="__l" bindupdateModelValue="{{g}}" u-p="{{h}}"><button class="ss-reset-button login-btn-start data-v-f21c903c" bindtap="{{f}}" slot="right">登录</button></uni-easyinput></uni-forms-item></uni-forms></view>