<view class="uni-calendar" bindmouseleave="{{Q}}"><view wx:if="{{a}}" class="{{['uni-calendar__mask', b && 'uni-calendar--mask-show']}}" bindtap="{{c}}"></view><view wx:if="{{d}}" class="{{['uni-calendar__content', N && 'uni-calendar--fixed', O && 'uni-calendar--ani-show', P && 'uni-calendar__content-mobile']}}"><view class="{{['uni-calendar__header', n && 'uni-calendar__header-mobile']}}"><view wx:if="{{e}}" class="uni-calendar__header-btn-box" catchtap="{{f}}"><view class="uni-calendar__header-btn uni-calendar--left"></view></view><picker mode="date" value="{{h}}" fields="month" bindchange="{{i}}"><text class="uni-calendar__header-text">{{g}}</text></picker><view wx:if="{{j}}" class="uni-calendar__header-btn-box" catchtap="{{k}}"><view class="uni-calendar__header-btn uni-calendar--right"></view></view><view wx:if="{{l}}" class="dialog-close" bindtap="{{m}}"><view class="dialog-close-plus" data-id="close"></view><view class="dialog-close-plus dialog-close-rotate" data-id="close"></view></view></view><view class="uni-calendar__box"><view wx:if="{{o}}" class="uni-calendar__box-bg"><text class="uni-calendar__box-bg-text">{{p}}</text></view><view class="uni-calendar__weeks" style="padding-bottom:7px"><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{q}}</text></view><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{r}}</text></view><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{s}}</text></view><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{t}}</text></view><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{v}}</text></view><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{w}}</text></view><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{x}}</text></view></view><view wx:for="{{y}}" wx:for-item="item" wx:key="b" class="uni-calendar__weeks"><view wx:for="{{item.a}}" wx:for-item="weeks" wx:key="e" class="uni-calendar__weeks-item"><calendar-item wx:if="{{weeks.d}}" class="uni-calendar-item--hook" bindchange="{{weeks.a}}" bindhandleMouse="{{weeks.b}}" u-i="{{weeks.c}}" bind:__l="__l" u-p="{{weeks.d}}"></calendar-item></view></view></view><view wx:if="{{z}}" class="uni-date-changed uni-calendar--fixed-top" style="padding:0 80px"><view class="uni-date-changed--time-date">{{A}}</view><time-picker wx:if="{{C}}" class="time-picker-style" u-i="85fae274-1" bind:__l="__l" bindupdateModelValue="{{B}}" u-p="{{C}}"></time-picker></view><view wx:if="{{D}}" class="uni-date-changed uni-calendar--fixed-top"><view class="uni-date-changed--time-start"><view class="uni-date-changed--time-date">{{E}}</view><time-picker wx:if="{{G}}" class="time-picker-style" u-i="85fae274-2" bind:__l="__l" bindupdateModelValue="{{F}}" u-p="{{G}}"></time-picker></view><uni-icons wx:if="{{H}}" style="line-height:50px" u-i="85fae274-3" bind:__l="__l" u-p="{{H}}"></uni-icons><view class="uni-date-changed--time-end"><view class="uni-date-changed--time-date">{{I}}</view><time-picker wx:if="{{K}}" class="time-picker-style" u-i="85fae274-4" bind:__l="__l" bindupdateModelValue="{{J}}" u-p="{{K}}"></time-picker></view></view><view wx:if="{{L}}" class="uni-date-changed uni-date-btn--ok"><view class="uni-datetime-picker--btn" bindtap="{{M}}">确认</view></view></view></view>