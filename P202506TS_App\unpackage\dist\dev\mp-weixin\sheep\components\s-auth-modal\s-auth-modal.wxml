<su-popup wx:if="{{C}}" class="data-v-********" u-s="{{['d']}}" bindclose="{{B}}" u-i="********-0" bind:__l="__l" u-p="{{C}}"><view class="login-wrap data-v-********"><account-login wx:if="{{a}}" class="data-v-********" bindonConfirm="{{b}}" u-i="********-1,********-0" bind:__l="__l" u-p="{{c}}"/><sms-login wx:if="{{d}}" class="data-v-********" bindonConfirm="{{e}}" u-i="********-2,********-0" bind:__l="__l" u-p="{{f}}"/><reset-password wx:if="{{g}}" class="data-v-********" u-i="********-3,********-0" bind:__l="__l"/><change-mobile wx:if="{{h}}" class="data-v-********" u-i="********-4,********-0" bind:__l="__l"/><change-password wx:if="{{i}}" class="data-v-********" u-i="********-5,********-0" bind:__l="__l"/><mp-authorization wx:if="{{j}}" class="data-v-********" u-i="********-6,********-0" bind:__l="__l"/><view wx:if="{{k}}" class="auto-login-box ss-flex ss-flex-col ss-row-center ss-col-center data-v-********"><view wx:if="{{l}}" class="ss-flex register-box data-v-********"><view class="register-title data-v-********">还没有账号?</view><button class="ss-reset-button login-btn data-v-********" open-type="getPhoneNumber" bindgetphonenumber="{{m}}"> 快捷登录 </button><view class="circle data-v-********"/></view><button wx:if="{{n}}" bindtap="{{p}}" class="ss-reset-button auto-login-btn data-v-********"><image class="auto-login-img data-v-********" src="{{o}}"/></button><button wx:if="{{q}}" bindtap="{{s}}" class="ss-reset-button auto-login-btn data-v-********"><image class="auto-login-img data-v-********" src="{{r}}"/></button></view><view wx:if="{{t}}" class="{{['agreement-box', 'ss-flex', 'ss-row-center', 'data-v-********', A && 'shake']}}"><label class="radio ss-flex ss-col-center data-v-********" bindtap="{{z}}"><radio class="data-v-********" checked="{{v}}" color="var(--ui-BG-Main)" style="transform:scale(0.8)" catchtap="{{w}}"/><view class="agreement-text ss-flex ss-col-center ss-m-l-8 data-v-********"> 我已阅读并遵守 <view class="tcp-text data-v-********" catchtap="{{x}}"> 《用户协议》 </view><view class="agreement-text data-v-********">与</view><view class="tcp-text data-v-********" catchtap="{{y}}"> 《隐私协议》 </view></view></label></view><view class="safe-box data-v-********"/></view></su-popup>