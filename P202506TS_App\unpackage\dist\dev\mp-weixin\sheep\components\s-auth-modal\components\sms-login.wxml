<view class="data-v-7636e637"><view class="head-box ss-m-b-60 data-v-7636e637"><view class="ss-flex ss-m-b-20 data-v-7636e637"><view class="head-title head-title-line head-title-animation data-v-7636e637">短信登录</view><view class="head-title-active ss-m-r-40 data-v-7636e637" bindtap="{{a}}"> 账号登录 </view></view><view class="head-subtitle data-v-7636e637">未注册的手机号，验证后自动注册账号</view></view><uni-forms wx:if="{{o}}" class="r data-v-7636e637" u-s="{{['d']}}" u-r="smsLoginRef" u-i="7636e637-0" bind:__l="__l" bindupdateModelValue="{{n}}" u-p="{{o}}"><uni-forms-item wx:if="{{h}}" class="data-v-7636e637" u-s="{{['d']}}" u-i="7636e637-1,7636e637-0" bind:__l="__l" u-p="{{h}}"><uni-easyinput wx:if="{{g}}" class="data-v-7636e637" u-s="{{['right']}}" u-i="7636e637-2,7636e637-1" bind:__l="__l" bindupdateModelValue="{{f}}" u-p="{{g}}"><button disabled="{{c}}" class="{{['ss-reset-button', 'code-btn', 'code-btn-start', 'data-v-7636e637', d && 'code-btn-end']}}" bindtap="{{e}}" slot="right">{{b}}</button></uni-easyinput></uni-forms-item><uni-forms-item wx:if="{{l}}" class="data-v-7636e637" u-s="{{['d']}}" u-i="7636e637-3,7636e637-0" bind:__l="__l" u-p="{{l}}"><uni-easyinput wx:if="{{k}}" class="data-v-7636e637" u-s="{{['right']}}" u-i="7636e637-4,7636e637-3" bind:__l="__l" bindupdateModelValue="{{j}}" u-p="{{k}}"><button class="ss-reset-button login-btn-start data-v-7636e637" bindtap="{{i}}" slot="right"> 登录 </button></uni-easyinput></uni-forms-item></uni-forms></view>