<view class="uni-date"><view class="uni-date-editor" bindtap="{{s}}"><block wx:if="{{$slots.d}}"><slot></slot></block><block wx:else><view class="{{['uni-date-editor--x', q && 'uni-date-editor--x__disabled', r && 'uni-date-x--border']}}"><view wx:if="{{a}}" class="uni-date-x uni-date-single"><uni-icons wx:if="{{b}}" u-i="564cc12a-0" bind:__l="__l" u-p="{{b}}"></uni-icons><input class="uni-date__x-input" type="text" placeholder="{{c}}" disabled="{{true}}" value="{{d}}" bindinput="{{e}}"/></view><view wx:else class="uni-date-x uni-date-range"><uni-icons wx:if="{{f}}" u-i="564cc12a-1" bind:__l="__l" u-p="{{f}}"></uni-icons><input class="uni-date__x-input t-c" type="text" placeholder="{{g}}" disabled="{{true}}" value="{{h}}" bindinput="{{i}}"/><block wx:if="{{$slots.d}}"><slot></slot></block><block wx:else><view class="">{{j}}</view></block><input class="uni-date__x-input t-c" type="text" placeholder="{{k}}" disabled="{{true}}" value="{{l}}" bindinput="{{m}}"/></view><view wx:if="{{n}}" class="uni-date__icon-clear" catchtap="{{p}}"><uni-icons wx:if="{{o}}" u-i="564cc12a-2" bind:__l="__l" u-p="{{o}}"></uni-icons></view></view></block></view><view hidden="{{!t}}" class="uni-date-mask" bindtap="{{v}}"></view><view wx:if="{{w}}" ref="datePicker" hidden="{{!aA}}" class="uni-date-picker__container"><view wx:if="{{x}}" class="uni-date-single--x" style="{{O}}"><view class="uni-popper__arrow"></view><view wx:if="{{y}}" class="uni-date-changed popup-x-header"><input class="uni-date__input t-c" type="text" placeholder="{{z}}" value="{{A}}" bindinput="{{B}}"/><time-picker wx:if="{{H}}" u-s="{{['d']}}" style="width:100%" u-i="564cc12a-3" bind:__l="__l" bindupdateModelValue="{{G}}" u-p="{{H}}"><input class="uni-date__input t-c" type="text" placeholder="{{C}}" disabled="{{D}}" value="{{E}}" bindinput="{{F}}"/></time-picker></view><calendar wx:if="{{K}}" class="r" u-r="pcSingle" bindchange="{{J}}" style="padding:0 8px" u-i="564cc12a-4" bind:__l="__l" u-p="{{K}}"/><view wx:if="{{L}}" class="popup-x-footer"><text class="confirm" bindtap="{{N}}">{{M}}</text></view><view class="uni-date-popper__arrow"></view></view><view wx:else class="uni-date-range--x" style="{{az}}"><view class="uni-popper__arrow"></view><view wx:if="{{P}}" class="popup-x-header uni-date-changed"><view class="popup-x-header--datetime"><input class="uni-date__input uni-date-range__input" type="text" placeholder="{{Q}}" value="{{R}}" bindinput="{{S}}"/><time-picker wx:if="{{Y}}" u-s="{{['d']}}" u-i="564cc12a-5" bind:__l="__l" bindupdateModelValue="{{X}}" u-p="{{Y}}"><input class="uni-date__input uni-date-range__input" type="text" placeholder="{{T}}" disabled="{{U}}" value="{{V}}" bindinput="{{W}}"/></time-picker></view><uni-icons wx:if="{{Z}}" style="line-height:40px" u-i="564cc12a-6" bind:__l="__l" u-p="{{Z}}"></uni-icons><view class="popup-x-header--datetime"><input class="uni-date__input uni-date-range__input" type="text" placeholder="{{aa}}" value="{{ab}}" bindinput="{{ac}}"/><time-picker wx:if="{{ai}}" u-s="{{['d']}}" u-i="564cc12a-7" bind:__l="__l" bindupdateModelValue="{{ah}}" u-p="{{ai}}"><input class="uni-date__input uni-date-range__input" type="text" placeholder="{{ad}}" disabled="{{ae}}" value="{{af}}" bindinput="{{ag}}"/></time-picker></view></view><view class="popup-x-body"><calendar wx:if="{{an}}" class="r" u-r="left" bindchange="{{ak}}" bindfirstEnterCale="{{al}}" bindmonthSwitch="{{am}}" style="padding:0 8px" u-i="564cc12a-8" bind:__l="__l" u-p="{{an}}"/><calendar wx:if="{{as}}" class="r" u-r="right" bindchange="{{ap}}" bindfirstEnterCale="{{aq}}" bindmonthSwitch="{{ar}}" style="padding:0 8px;border-left:1px solid #F1F1F1" u-i="564cc12a-9" bind:__l="__l" u-p="{{as}}"/></view><view wx:if="{{at}}" class="popup-x-footer"><text class="" bindtap="{{aw}}">{{av}}</text><text class="confirm" bindtap="{{ay}}">{{ax}}</text></view></view></view><calendar wx:if="{{aE}}" class="r" data-c-h="{{!aC}}" u-r="mobile" bindconfirm="{{aD}}" u-i="564cc12a-10" bind:__l="__l" u-p="{{aE}}"/></view>