/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-section {
  background-color: #fff;
}
.uni-section .uni-section-header {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 12px 10px;
  font-weight: normal;
}
.uni-section .uni-section-header__decoration {
  margin-right: 6px;
  background-color: #2979ff;
}
.uni-section .uni-section-header__decoration.line {
  width: 4px;
  height: 12px;
  border-radius: 10px;
}
.uni-section .uni-section-header__decoration.circle {
  width: 8px;
  height: 8px;
  border-top-right-radius: 50px;
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;
  border-bottom-right-radius: 50px;
}
.uni-section .uni-section-header__decoration.square {
  width: 8px;
  height: 8px;
}
.uni-section .uni-section-header__content {
  display: flex;
  flex-direction: column;
  flex: 1;
  color: #333;
}
.uni-section .uni-section-header__content .distraction {
  flex-direction: row;
  align-items: center;
}
.uni-section .uni-section-header__content-sub {
  margin-top: 2px;
}
.uni-section .uni-section-header__slot-right {
  font-size: 14px;
}
.uni-section .uni-section-content {
  font-size: 14px;
}